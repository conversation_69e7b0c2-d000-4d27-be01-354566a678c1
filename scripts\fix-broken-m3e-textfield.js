#!/usr/bin/env node

/**
 * 修复被破坏的 M3ETextField 组件
 * 
 * 修复由于之前脚本导致的语法错误
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 修复文件中的语法错误
const fixBrokenSyntax = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 检查是否包含 M3ETextField
    if (!content.includes('M3ETextField')) {
      return false;
    }

    // 修复破损的 className 属性
    const brokenClassPattern = /className="[^"]*?-outline-300[^"]*?dark:[^"]*?"/g;
    if (brokenClassPattern.test(content)) {
      content = content.replace(brokenClassPattern, 'className="mb-4"');
      modified = true;
      console.log(`  修复破损的 className 属性`);
    }

    // 修复破损的自闭合标签
    const brokenSelfClosingPattern = /\s*\/\s*\/>/g;
    if (brokenSelfClosingPattern.test(content)) {
      content = content.replace(brokenSelfClosingPattern, ' />');
      modified = true;
      console.log(`  修复破损的自闭合标签`);
    }

    // 修复多余的空格和换行
    const extraSpacePattern = /(\s+)\/\s*>/g;
    content = content.replace(extraSpacePattern, '\n      />');

    // 修复属性格式
    const attributePattern = /(M3ETextField[^>]*?)\s+([a-zA-Z]+)=/g;
    content = content.replace(attributePattern, (match, before, attr) => {
      if (!before.endsWith('\n        ') && !before.endsWith(' ')) {
        modified = true;
        return `${before}\n        ${attr}=`;
      }
      return match;
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复破损的 M3ETextField 语法...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let fixedCount = 0;
  
  for (const file of allFiles) {
    const fixed = fixBrokenSyntax(file);
    if (fixed) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 修复了 ${fixedCount} 个文件中的语法错误`);
  
  if (fixedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 重新运行应用程序');
    console.log('2. 检查所有表单是否正常工作');
    console.log('3. 验证 M3ETextField 组件显示正确');
  }
};

main();
