import React from 'react';
import { Text } from '@/components/base';
import { SafeAreaView } from '@/components/ui/safe-area-view';
import ProfileScreenContent from '@/features/profile/screens/profile-screen';

// This route file simply imports and renders the feature screen
// It also handles the top-level SafeAreaView for this route segment
export default function ProfileRoute() {
  return (
    <SafeAreaView className="flex-1 bg-background-950">
      <ProfileScreenContent />
    </SafeAreaView>
  );
}

/*
// Original placeholder content (commented out)

import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

import { useTranslation } from 'react-i18next';

export default function ProfileScreen_Placeholder() {
  const { t } = useTranslation();
  const { mode } = useUnifiedTheme();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ color: colors.text, fontSize: 20 }}>
        {t('profile')} {t('testPlaceholder')}
      </Text>
    </SafeAreaView>
  );
}
*/
