/**
 * 修复导入冲突脚本
 * 
 * 修复由于迁移脚本导致的重复导入问题
 */

const fs = require('fs');
const path = require('path');

// 需要检查的文件模式
const filePatterns = [
  'features/**/*.tsx',
  'components/**/*.tsx',
  'app/**/*.tsx'
];

// 获取所有 TypeScript 文件
function getAllTsxFiles() {
  const files = [];
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过 node_modules 和其他不需要的目录
          if (!['node_modules', '.git', '.expo', 'dist', 'build'].includes(item)) {
            scanDirectory(fullPath);
          }
        } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
  }
  
  scanDirectory('.');
  return files;
}

// 修复单个文件的导入冲突
function fixImportConflicts(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复重复的 M3EProgressIndicator 导入
    const progressIndicatorPattern = /import\s*{\s*M3EProgressIndicator\s*}\s*from\s*['"]@\/components\/ui\/m3e-progress-indicator['"];?\s*\n.*import\s*{\s*([^}]*M3EProgressIndicator[^}]*)\s*}\s*from\s*['"]@\/components\/ui\/m3e-button\/m3e-buttons['"];?/g;
    
    content = content.replace(progressIndicatorPattern, (match, buttonImports) => {
      // 移除 M3EProgressIndicator 从 button imports 中
      const cleanedImports = buttonImports
        .split(',')
        .map(imp => imp.trim())
        .filter(imp => !imp.includes('M3EProgressIndicator'))
        .join(', ');
      
      modified = true;
      return `import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';\nimport { ${cleanedImports} } from '@/components/ui/m3e-button/m3e-buttons';`;
    });
    
    // 2. 修复重复的其他组件导入
    const duplicateImportPattern = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]([^'"]+)['"];?\s*\n.*import\s*{\s*([^}]*)\s*}\s*from\s*['"](\2)['"];?/g;
    
    content = content.replace(duplicateImportPattern, (match, imports1, path1, imports2, path2) => {
      if (path1 === path2) {
        // 合并重复的导入
        const allImports = [...new Set([
          ...imports1.split(',').map(imp => imp.trim()),
          ...imports2.split(',').map(imp => imp.trim())
        ])];
        
        modified = true;
        return `import { ${allImports.join(', ')} } from '${path1}';`;
      }
      return match;
    });
    
    // 3. 修复错误的组件使用
    // 修复 M3ETextField 嵌套使用
    const textFieldPattern = /<M3ETextField[^>]*>\s*<M3ETextField/g;
    if (textFieldPattern.test(content)) {
      content = content.replace(textFieldPattern, '<M3ETextField');
      content = content.replace(/<\/M3ETextField>\s*<\/M3ETextField>/g, '</M3ETextField>');
      modified = true;
    }
    
    // 4. 修复 Button 属性
    // 移除不兼容的属性
    const buttonPropsPattern = /(M3EButtonFilled[^>]*)\s+(action|variant|size|isDisabled)=["'][^"']*["']/g;
    content = content.replace(buttonPropsPattern, (match, buttonStart, prop) => {
      if (['action', 'variant', 'size'].includes(prop)) {
        modified = true;
        return buttonStart;
      }
      if (prop === 'isDisabled') {
        modified = true;
        return buttonStart.replace('isDisabled', 'disabled');
      }
      return match;
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🚀 开始修复导入冲突...\n');
  
  const files = getAllTsxFiles();
  let totalFiles = 0;
  let fixedFiles = 0;
  
  for (const file of files) {
    totalFiles++;
    if (fixImportConflicts(file)) {
      fixedFiles++;
    }
  }
  
  console.log(`\n📊 修复完成:`);
  console.log(`   检查文件数: ${totalFiles}`);
  console.log(`   已修复: ${fixedFiles}`);
  console.log(`   无需修复: ${totalFiles - fixedFiles}`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixImportConflicts };
