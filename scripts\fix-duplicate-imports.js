#!/usr/bin/env node

/**
 * 修复重复导入问题
 * 
 * 特别是 M3EProgressIndicator 的重复导入
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 修复重复导入
const fixDuplicateImports = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 检查是否有重复的 M3EProgressIndicator 导入
    const progressIndicatorImports = content.match(/import.*M3EProgressIndicator.*from.*$/gm);
    if (progressIndicatorImports && progressIndicatorImports.length > 1) {
      console.log(`  发现 ${progressIndicatorImports.length} 个 M3EProgressIndicator 导入`);
      
      // 移除从 m3e-button 中的 M3EProgressIndicator 导入
      content = content.replace(
        /import\s*{\s*([^}]*),\s*M3EProgressIndicator\s*([^}]*)\s*}\s*from\s*['"]@\/components\/ui\/m3e-button\/m3e-buttons['"];?/g,
        (match, before, after) => {
          modified = true;
          const cleanBefore = before.trim();
          const cleanAfter = after.trim();
          let imports = [];
          if (cleanBefore) imports.push(cleanBefore);
          if (cleanAfter) imports.push(cleanAfter);
          return `import { ${imports.join(', ')} } from '@/components/ui/m3e-button/m3e-buttons';`;
        }
      );

      // 确保有正确的 M3EProgressIndicator 导入
      if (!content.includes("import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator'")) {
        // 在其他导入后添加
        const lastImportMatch = content.match(/^import.*$/gm);
        if (lastImportMatch) {
          const lastImport = lastImportMatch[lastImportMatch.length - 1];
          const insertIndex = content.indexOf(lastImport) + lastImport.length;
          content = content.slice(0, insertIndex) + 
            "\nimport { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';" + 
            content.slice(insertIndex);
          modified = true;
        }
      }
    }

    // 修复其他可能的重复导入
    const importLines = content.match(/^import.*$/gm) || [];
    const seenImports = new Set();
    const duplicateImports = [];

    for (const line of importLines) {
      const normalized = line.trim();
      if (seenImports.has(normalized)) {
        duplicateImports.push(normalized);
      } else {
        seenImports.add(normalized);
      }
    }

    if (duplicateImports.length > 0) {
      console.log(`  发现 ${duplicateImports.length} 个重复导入`);
      for (const duplicate of duplicateImports) {
        // 移除重复的导入（保留第一个）
        const regex = new RegExp('^' + duplicate.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '$', 'gm');
        const matches = content.match(regex);
        if (matches && matches.length > 1) {
          // 移除除第一个之外的所有匹配
          let count = 0;
          content = content.replace(regex, (match) => {
            count++;
            return count === 1 ? match : '';
          });
          modified = true;
        }
      }
    }

    // 清理空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复重复导入问题...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let fixedCount = 0;
  
  for (const file of allFiles) {
    const fixed = fixDuplicateImports(file);
    if (fixed) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 修复了 ${fixedCount} 个文件中的重复导入问题`);
  
  if (fixedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 重新运行应用程序');
    console.log('2. 检查所有导入是否正确');
    console.log('3. 验证组件功能正常');
  }
};

main();
