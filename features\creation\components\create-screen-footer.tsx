import React from 'react';
import { View } from 'react-native';
import { M3EButtonFilled } from '@/components/ui/m3e-button/m3e-buttons';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

import { ArrowRight } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface CreateScreenFooterProps {
  onNextPress: () => void;
  canProceed: boolean;
  nextButtonText?: string; // Allow customizing button text
}

export function CreateScreenFooter({
  onNextPress,
  canProceed,
  nextButtonText,
}: CreateScreenFooterProps) {
  const { isDark } = useUnifiedTheme();
  const { t } = useTranslation();

  const buttonText = nextButtonText || t('common.next', '下一步');

  return (
    <View className="p-4 border-t border-m3-outline-variant bg-m3-surface-main">
      <M3EButtonFilled
        onPress={onNextPress}
        disabled={!canProceed}
        className="flex-row items-center justify-center py-3 rounded-xl"
        style={{ opacity: canProceed ? 1 : 0.5 }}
        icon={<ArrowRight size={20} />}
        iconPosition="trailing"
      >
        {buttonText}
      </M3EButtonFilled>
    </View>
  );
}
