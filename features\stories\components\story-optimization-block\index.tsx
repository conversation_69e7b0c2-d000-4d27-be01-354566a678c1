import React, { useState } from 'react';
import { Modal, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Wand2 } from 'lucide-react-native';

// Import gluestack-ui components

import { Text } from '@/components/base';
import {
  M3EButtonFilled,
  M3EButtonText,
} from '@/components/ui/m3e-button/m3e-buttons';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

import { TextArea, TextAreaInput } from '@/components/base';
import { Pressable } from '@/components/base';

type OptimizationType =
  | 'grammar'
  | 'style'
  | 'creativity'
  | 'conciseness'
  | 'all';

interface StoryOptimizationBlockProps {
  onOptimizeContent: (content: string, type: OptimizationType) => void;
  isOptimizing: boolean;
  disabled?: boolean;
}

export default function StoryOptimizationBlock({
  onOptimizeContent,
  isOptimizing,
  disabled = false,
}: StoryOptimizationBlockProps) {
  const { t } = useTranslation();

  const [modalVisible, setModalVisible] = useState(false);
  const [contentToOptimize, setContentToOptimize] = useState('');
  const [selectedType, setSelectedType] = useState<OptimizationType>('all');

  const optimizationTypes: { value: OptimizationType; label: string }[] = [
    { value: 'grammar', label: t('storyOptimization.types.grammar', '语法') },
    { value: 'style', label: t('storyOptimization.types.style', '风格') },
    {
      value: 'creativity',
      label: t('storyOptimization.types.creativity', '创意'),
    },
    {
      value: 'conciseness',
      label: t('storyOptimization.types.conciseness', '简洁'),
    },
    { value: 'all', label: t('storyOptimization.types.all', '全面优化') },
  ];

  const handleOptimize = () => {
    if (contentToOptimize.trim()) {
      onOptimizeContent(contentToOptimize, selectedType);
      setModalVisible(false);
    }
  };

  return (
    <View className="mt-4 mb-6">
      <M3EButtonFilled
        action="secondary"
        variant="solid"
        size="md"
        className="py-3 px-4 rounded-md bg-secondary-500 dark:bg-secondary-600"
        onPress={() => setModalVisible(true)}
        isDisabled={isOptimizing || disabled}
      >
        {isOptimizing ? (
          <M3EProgressIndicator size="small" color="$background50" />
        ) : (
          <>
            <ButtonIcon as={Wand2} size="md" className="mr-2" />
            <M3EButtonText className="font-bold">
              {t('storyOptimization.button', 'AI 优化内容')}
            </ButtonText>
          </>
        )}
      </Button>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View className="flex-1 justify-center items-center bg-black/50 p-4">
          <View className="w-full max-w-md bg-background-50 dark:bg-background-900 rounded-xl p-5 shadow-md">
            <Text className="text-xl font-bold text-typography-900 dark:text-typography-100 mb-4 text-center">
              {t('storyOptimization.modalTitle', 'AI 内容优化')}
            </Text>

            <Text className="text-sm font-medium text-typography-900 dark:text-typography-100 mb-2">
              {t('storyOptimization.contentLabel', '需要优化的内容:')}
            </Text>
            <TextArea size="md" className="mb-4">
              <TextAreaInput
                placeholder={t(
                  'storyOptimization.contentPlaceholder',
                  '请输入需要优化的内容...'
                )}
                value={contentToOptimize}
                onChangeText={setContentToOptimize}
                className="min-h-[150px]"
              />
            </TextArea>

            <Text className="text-sm font-medium text-typography-900 dark:text-typography-100 mb-2">
              {t('storyOptimization.typeLabel', '优化类型:')}
            </Text>
            <View className="flex-row flex-wrap mb-6">
              {optimizationTypes.map((type) => (
                <Pressable
                  key={type.value}
                  className={`border rounded-md py-1 px-3 mr-2 mb-2 ${
                    selectedType === type.value
                      ? 'bg-primary-500 border-primary-500'
                      : 'border-outline-300 dark:border-outline-700'
                  }`}
                  onPress={() => setSelectedType(type.value)}
                >
                  <Text
                    className={`text-sm font-medium ${
                      selectedType === type.value
                        ? 'text-background-50'
                        : 'text-typography-700 dark:text-typography-300'
                    }`}
                  >
                    {type.label}
                  </Text>
                </Pressable>
              ))}
            </View>

            <View space="md" className="flex flex-row mt-4">
              <M3EButtonFilled
                action="secondary"
                variant="outline"
                size="md"
                className="flex-1"
                onPress={() => setModalVisible(false)}
              >
                <M3EButtonText>{t('common.cancel', '取消')}</ButtonText>
              </Button>

              <M3EButtonFilled
                action="primary"
                variant="solid"
                size="md"
                className="flex-1"
                onPress={handleOptimize}
                isDisabled={!contentToOptimize.trim() || isOptimizing}
              >
                {isOptimizing ? (
                  <M3EProgressIndicator size="small" color="$background50" />
                ) : (
                  <M3EButtonText>
                    {t('storyOptimization.optimize', '优化')}
                  </ButtonText>
                )}
              </Button>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
