#!/usr/bin/env node

/**
 * 综合性组件和主题清理脚本
 * 
 * 解决两个核心问题：
 * 1. 重复组件清理 - 移除兼容层，统一使用 M3E 组件
 * 2. 主题系统优化 - 基于 NativeWind 最佳实践重构主题系统
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 1. 组件清理：移除兼容层使用
const cleanupCompatibilityLayers = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const issues = [];

    // 替换兼容层导入为直接 M3E 组件导入
    const compatImportPatterns = [
      {
        pattern: /import\s*{\s*([^}]*Button[^}]*)\s*}\s*from\s*['"]@\/components\/ui\/m3e-button['"];?/g,
        replacement: "import { M3EButtonFilled, M3EButtonText, M3EButtonOutlined, M3EButtonTonal, M3EButtonElevated } from '@/components/ui/m3e-button/m3e-buttons';",
        message: '替换 m3e-button 兼容层导入'
      },
      {
        pattern: /import\s*{\s*([^}]*Input[^}]*)\s*}\s*from\s*['"]@\/components\/ui\/m3e-text-field['"];?/g,
        replacement: "import { M3ETextField } from '@/components/ui/m3e-text-field/m3e-text-field';",
        message: '替换 m3e-text-field 兼容层导入'
      }
    ];

    for (const { pattern, replacement, message } of compatImportPatterns) {
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement);
        modified = true;
        issues.push(message);
      }
    }

    // 替换组件使用
    const componentReplacements = [
      { from: /<Button\b/g, to: '<M3EButtonFilled', message: '替换 Button 为 M3EButtonFilled' },
      { from: /<ButtonText\b/g, to: '<M3EButtonText', message: '替换 ButtonText 为 M3EButtonText' },
      { from: /<Input\b/g, to: '<M3ETextField', message: '替换 Input 为 M3ETextField' },
      { from: /<InputField\b/g, to: '<M3ETextField', message: '替换 InputField 为 M3ETextField' }
    ];

    for (const { from, to, message } of componentReplacements) {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
        issues.push(message);
      }
    }

    return { content, modified, issues };
  } catch (error) {
    console.error(`处理兼容层清理时出错: ${error.message}`);
    return { content: '', modified: false, issues: [] };
  }
};

// 2. 主题系统优化：统一使用 useUnifiedTheme
const optimizeThemeUsage = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const issues = [];

    // 替换旧的主题 Hook 导入
    const oldThemeImports = [
      /import\s*{\s*([^}]*useTheme[^}]*)\s*}\s*from\s*['"]@\/lib\/theme\/theme-provider['"];?/g,
      /import\s*{\s*([^}]*useAppTheme[^}]*)\s*}\s*from\s*['"][^'"]*['"];?/g
    ];

    for (const pattern of oldThemeImports) {
      if (pattern.test(content)) {
        content = content.replace(pattern, "import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';");
        modified = true;
        issues.push('替换旧的主题 Hook 导入');
      }
    }

    // 替换主题 Hook 使用
    const themeHookReplacements = [
      { from: /\buseTheme\(\)/g, to: 'useUnifiedTheme()', message: '替换 useTheme() 为 useUnifiedTheme()' },
      { from: /\buseAppTheme\(\)/g, to: 'useUnifiedTheme()', message: '替换 useAppTheme() 为 useUnifiedTheme()' }
    ];

    for (const { from, to, message } of themeHookReplacements) {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
        issues.push(message);
      }
    }

    // 替换硬编码颜色为语义化类名
    const colorReplacements = [
      { from: /className="([^"]*?)bg-blue-\d+([^"]*?)"/g, to: 'className="$1bg-m3-primary-main$2"', message: '替换硬编码蓝色为主色' },
      { from: /className="([^"]*?)text-blue-\d+([^"]*?)"/g, to: 'className="$1text-m3-primary-main$2"', message: '替换硬编码蓝色文字为主色文字' },
      { from: /className="([^"]*?)bg-gray-\d+([^"]*?)"/g, to: 'className="$1bg-m3-surface-variant$2"', message: '替换硬编码灰色为表面变体色' },
      { from: /className="([^"]*?)text-gray-\d+([^"]*?)"/g, to: 'className="$1text-m3-surface-on-variant$2"', message: '替换硬编码灰色文字为表面变体文字色' }
    ];

    for (const { from, to, message } of colorReplacements) {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
        issues.push(message);
      }
    }

    return { content, modified, issues };
  } catch (error) {
    console.error(`处理主题优化时出错: ${error.message}`);
    return { content: '', modified: false, issues: [] };
  }
};

// 3. M3E 组件样式优化：使用动态主题
const optimizeM3EComponentThemes = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const issues = [];

    // 检查是否是 M3E 组件文件
    if (!filePath.includes('m3e-') || !filePath.includes('components/ui/')) {
      return { content, modified, issues };
    }

    // 确保 M3E 组件使用 useUnifiedTheme
    if (content.includes('export const M3E') && !content.includes('useUnifiedTheme')) {
      // 添加 useUnifiedTheme 导入
      const importMatch = content.match(/^import.*$/gm);
      if (importMatch) {
        const lastImport = importMatch[importMatch.length - 1];
        const insertIndex = content.indexOf(lastImport) + lastImport.length;
        content = content.slice(0, insertIndex) + 
          "\nimport { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';" + 
          content.slice(insertIndex);
        modified = true;
        issues.push('添加 useUnifiedTheme 导入到 M3E 组件');
      }
    }

    // 替换硬编码颜色为动态主题颜色
    if (content.includes('useUnifiedTheme')) {
      const hardcodedColorPatterns = [
        { from: /#[0-9A-Fa-f]{6}/g, to: 'colors.primary', message: '替换硬编码十六进制颜色' },
        { from: /rgba?\([^)]+\)/g, to: 'colors.surface', message: '替换硬编码 RGB 颜色' }
      ];

      for (const { from, to, message } of hardcodedColorPatterns) {
        if (from.test(content)) {
          // 这里需要更智能的替换逻辑，暂时标记需要手动处理
          issues.push(`发现${message}，需要手动优化`);
        }
      }
    }

    return { content, modified, issues };
  } catch (error) {
    console.error(`处理 M3E 组件主题优化时出错: ${error.message}`);
    return { content: '', modified: false, issues: [] };
  }
};

// 综合处理函数
const comprehensiveCleanup = (filePath) => {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    let allIssues = [];
    let finalModified = false;

    // 1. 兼容层清理
    const compatResult = cleanupCompatibilityLayers(filePath);
    let currentContent = compatResult.modified ? compatResult.content : originalContent;
    allIssues = allIssues.concat(compatResult.issues);
    finalModified = finalModified || compatResult.modified;

    // 2. 主题系统优化
    const themeResult = optimizeThemeUsage(filePath);
    currentContent = themeResult.modified ? themeResult.content : currentContent;
    allIssues = allIssues.concat(themeResult.issues);
    finalModified = finalModified || themeResult.modified;

    // 3. M3E 组件优化
    const m3eResult = optimizeM3EComponentThemes(filePath);
    currentContent = m3eResult.modified ? m3eResult.content : currentContent;
    allIssues = allIssues.concat(m3eResult.issues);
    finalModified = finalModified || m3eResult.modified;

    if (finalModified) {
      fs.writeFileSync(filePath, currentContent, 'utf8');
      console.log(`✅ 已优化: ${filePath}`);
      allIssues.forEach(issue => console.log(`  - ${issue}`));
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始综合性组件和主题清理...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let optimizedCount = 0;
  
  for (const file of allFiles) {
    const optimized = comprehensiveCleanup(file);
    if (optimized) {
      optimizedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 优化了 ${optimizedCount} 个文件`);
  
  if (optimizedCount > 0) {
    console.log('\n📝 下一步建议：');
    console.log('1. 移除兼容层文件：');
    console.log('   - components/ui/m3e-button/index.tsx');
    console.log('   - components/ui/m3e-text-field/m3e-input-compat.tsx');
    console.log('2. 统一主题配置文件');
    console.log('3. 测试应用程序功能');
    console.log('4. 手动优化 M3E 组件中的硬编码颜色');
  }
};

main();
