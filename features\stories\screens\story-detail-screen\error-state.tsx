import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Text  } from '@/components/base';
import { M3EButtonFilled, M3EButtonText } from '@/components/ui/m3e-button/m3e-buttons';

interface ErrorStateProps {
  errorMessage: string;
  onRetry: () => void;
}

export function ErrorState({ errorMessage, onRetry }: ErrorStateProps) {
  const { t } = useTranslation();

  return (
    <View className="flex-1 justify-center items-center p-4 bg-background-0">
      <Text className="text-base font-medium text-error-600 text-center mb-4">
        {errorMessage}
      </Text>
      <M3EButtonFilled
        className="bg-primary-500 py-2 px-6 rounded-md items-center justify-center min-h-[44px]"
        onPress={onRetry}
      >
        <M3EButtonText className="text-base font-bold text-white">
          {t('tryAgain', 'Try Again')}
        </M3EButtonText>
      </M3EButtonFilled>
    </View>
  );
}
