import React from 'react';
import { FlatList, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { Plus } from 'lucide-react-native';
import { useConversations } from '../../hooks/use-conversations';
import ConversationItem from '../conversation-item';
import EmptyConversationsState from '../empty-conversations-state';
import { Conversation } from '@/api/messages/types';

// Import M3E components
import { M3EButtonFilled, M3EIconButton } from '@/components/ui/m3e-button/m3e-buttons';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

export default function MessagesTab() {
  const { t } = useTranslation();
  const router = useRouter();

  const {
    conversations,
    isLoading,
    hasMore,
    refreshConversations,
    loadMoreConversations,
  } = useConversations({ autoRefresh: true });

  // 处理对话点击
  const handleConversationPress = (conversation: Conversation) => {
    router.push(`/messages/${conversation.id}`);
  };

  // 处理新建对话
  const handleNewConversation = () => {
    router.push('/messages/new');
  };

  // 渲染列表底部
  const renderFooter = () => {
    if (!isLoading) return null;
    return (
      <View className="p-4 items-center">
        <M3EProgressIndicator size="small" color="$primary500" />
      </View>
    );
  };

  // 渲染空状态
  const renderEmpty = () => {
    if (isLoading) return null;
    return (
      <EmptyConversationsState onNewConversation={handleNewConversation} />
    );
  };

  return (
    <View className="flex-1 bg-background-50 dark:bg-background-900">
      <FlatList
        data={conversations}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <ConversationItem
            conversation={item}
            onPress={handleConversationPress}
          />
        )}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        onRefresh={refreshConversations}
        refreshing={isLoading && conversations.length === 0}
        onEndReached={loadMoreConversations}
        onEndReachedThreshold={0.5}
        contentContainerStyle={
          conversations.length === 0 ? { flexGrow: 1 } : undefined
        }
      />

      <M3EButtonFilled
        position="absolute"
        bottom="$6"
        right="$6"
        size="lg"
        borderRadius="$full"
        width="$14"
        height="$14"
       
        onPress={handleNewConversation}
        className="items-center justify-center shadow-md"
      >
        <M3EIconButton icon={<Plus size="md" color="$background50"  />} />
      </M3EButtonFilled>
    </View>
  );
}
