import React from 'react';
import { View, Image } from 'react-native';
import { Notification } from '@/api/notifications/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';
import {
  Heart,
  MessageCircle,
  UserPlus,
  AtSign,
  BookOpen,
  Sparkle,
  Bell,
} from 'lucide-react-native';

import { Text, Pressable } from '@/components/base';

interface NotificationItemProps {
  notification: Notification;
  onPress: (notification: Notification) => void;
  onMarkAsRead: (notificationId: string) => void;
  onDelete: (notificationId: string) => void;
}

export default function NotificationItem({
  notification,
  onPress,
  onMarkAsRead,
  onDelete,
}: NotificationItemProps) {
  const { t, i18n } = useTranslation();

  // 根据通知类型获取图标
  const getNotificationIcon = () => {
    const iconSize = 20;
    const iconColor = '#333333'; // primary-500 color

    switch (notification.type) {
      case 'like':
        return <Heart size={iconSize} color={iconColor} />;
      case 'comment':
        return <MessageCircle size={iconSize} color={iconColor} />;
      case 'follow':
        return <UserPlus size={iconSize} color={iconColor} />;
      case 'mention':
        return <AtSign size={iconSize} color={iconColor} />;
      case 'new_story':
        return <BookOpen size={iconSize} color={iconColor} />;
      case 'new_segment':
        return <Sparkle size={iconSize} color={iconColor} />;
      case 'system':
        return <Bell size={iconSize} color={iconColor} />;
      default:
        return <Bell size={iconSize} color={iconColor} />;
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: i18n.language === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // 处理通知点击
  const handlePress = () => {
    if (!notification.is_read) {
      onMarkAsRead(notification.id);
    }
    onPress(notification);
  };

  // 处理删除按钮点击
  const handleDelete = () => {
    onDelete(notification.id);
  };

  return (
    <Pressable className={`flex-row p-4 border-b border-outline-300 ${
        !notification.is_read
          ? 'bg-background-100 border-l-3 border-l-primary-500'
          : 'bg-background-0'
      }`}
      onPress={handlePress}
    >
      <View className="w-10 h-10 rounded-full bg-background-200 justify-center items-center mr-4">
        {getNotificationIcon()}
      </View>

      <View className="flex-1">
        {notification.actor && (
          <View className="flex-row items-center mb-1">
            {notification.actor.avatar_url ? (
              <Image
                source={{ uri: notification.actor.avatar_url }}
                className="w-6 h-6 rounded-full mr-1"
                alt={notification.actor.username}
              />
            ) : (
              <View className="w-6 h-6 rounded-full mr-1 bg-background-300" />
            )}
            <Text className="text-sm font-medium text-typography-800">
              {notification.actor.username}
            </Text>
          </View>
        )}

        <Text className="text-base font-medium text-typography-900 mb-1">
          {notification.title}
        </Text>
        <Text className="text-sm text-typography-600 mb-1" numberOfLines={2}>
          {notification.body}
        </Text>

        <Text className="text-xs text-typography-500">
          {formatTime(notification.created_at)}
        </Text>
      </View>

      <Pressable className="justify-center px-4" onPress={handleDelete}>
        <Text className="text-sm font-medium text-error-600">
          {t('notifications.delete', '删除')}
        </Text>
      </Pressable>
    </Pressable>
  );
}
