import React from 'react';
import { View } from 'react-native';
import { ScrollView } from '@/components/ui/scroll-view';
import { Text, Pressable } from '@/components/base';


import { Book, Bookmark, Clock, Heart, PenLine } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

export type StoryTabKey =
  | 'drafts'
  | 'published'
  | 'reading'
  | 'favorites'
  | 'saved';

interface StoryTabsProps {
  activeTab: StoryTabKey;
  onTabPress: (tabKey: StoryTabKey) => void;
}

const TABS: StoryTabKey[] = [
  'drafts',
  'published',
  'reading',
  'favorites',
  'saved',
];

const renderTabIcon = (tabKey: StoryTabKey, active: boolean) => {
  const iconClass = active
    ? 'text-primary-500 dark:text-primary-400'
    : 'text-typography-500 dark:text-typography-400';
  const size = 18; // Slightly smaller icon
  switch (tabKey) {
    case 'drafts':
      return <PenLine size={size} className={iconClass} />;
    case 'published':
      return <Book size={size} className={iconClass} />;
    case 'reading':
      return <Clock size={size} className={iconClass} />;
    case 'favorites':
      return <Heart size={size} className={iconClass} />;
    case 'saved':
      return <Bookmark size={size} className={iconClass} />;
    default:
      return null;
  }
};

const getTabTitleKey = (tabKey: StoryTabKey): string => {
  switch (tabKey) {
    case 'drafts':
      return 'storyTabDrafts';
    case 'published':
      return 'storyTabPublished';
    case 'reading':
      return 'storyTabReading';
    case 'favorites':
      return 'storyTabFavorites';
    case 'saved':
      return 'storyTabSaved';
    default:
      return '';
  }
};

export function StoryTabs({ activeTab, onTabPress }: StoryTabsProps) {
  const { t } = useTranslation();

  return (
    <View className="border-b border-outline-200 dark:border-outline-700 bg-background-50 dark:bg-background-950 px-4 pb-1">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerClassName="py-2 items-center gap-2"
      >
        {TABS.map((tabKey) => {
          const isActive = activeTab === tabKey;

          return (
            <Pressable key={tabKey}
              className={`flex-row items-center px-4 py-2 rounded-full ${
                isActive
                  ? 'bg-primary-100 dark:bg-primary-900'
                  : 'bg-transparent'
              }`}
              onPress={() => onTabPress(tabKey)}
            >
              {renderTabIcon(tabKey, isActive)}
              <Text
                className={`ml-1 font-medium ${
                  isActive
                    ? 'text-primary-500 dark:text-primary-400'
                    : 'text-typography-500 dark:text-typography-400'
                }`}
              >
                {t(getTabTitleKey(tabKey))}
              </Text>
            </Pressable>
          );
        })}
      </ScrollView>
    </View>
  );
}
