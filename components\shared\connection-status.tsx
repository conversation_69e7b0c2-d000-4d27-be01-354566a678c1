import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNetworkStatus } from '@/hooks/use-network-status';

import { Text  } from '@/components/base';
// import { M3EButtonFilled, M3EButtonText, M3EButtonOutlined, M3EButtonTonal, M3EButtonElevated } from '@/components/ui/m3e-button/m3e-buttons';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

interface ConnectionStatusProps {
  showWhenHealthy?: boolean;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  showWhenHealthy = false,
}) => {
  const { t } = useTranslation();
  const {
    isConnected,
    isSupabaseHealthy,
    connectionType,
    isChecking,
    lastChecked,
    checkHealth,
  } = useNetworkStatus();

  // 如果连接正常且不需要显示，则不渲染
  if (isConnected && isSupabaseHealthy && !showWhenHealthy) {
    return null;
  }

  const getStatusColor = () => {
    if (!isConnected) return 'bg-error-500';
    if (!isSupabaseHealthy) return 'bg-warning-500';
    return 'bg-success-500';
  };

  const getStatusText = () => {
    if (!isConnected) {
      return t('network.disconnected', '网络连接已断开');
    }
    if (!isSupabaseHealthy) {
      return t('network.serviceUnavailable', '服务暂时不可用');
    }
    return t('network.connected', '连接正常');
  };

  const getStatusIcon = () => {
    if (isChecking) {
      return <M3EProgressIndicator className="text-white" size="small" />;
    }
    return null;
  };

  return (
    <View className={`${getStatusColor()} p-3 m-2 rounded-lg`}>
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center flex-1">
          {getStatusIcon()}
          <View className="ml-2 flex-1">
            <Text className="text-white font-medium">{getStatusText()}</Text>
            {connectionType && (
              <Text className="text-white text-sm opacity-80">
                {t('network.connectionType', '连接类型')}: {connectionType}
              </Text>
            )}
            {lastChecked && (
              <Text className="text-white text-xs opacity-70">
                {t('network.lastChecked', '上次检查')}:{' '}
                {lastChecked.toLocaleTimeString()}
              </Text>
            )}
          </View>
        </View>

        {/* Button 组件暂时注释，等待 m3e-button 模块导出问题解决 */}
        {/*
        {!isSupabaseHealthy && isConnected && (
          <M3EButtonFilled
            variant="outline"
            size="sm"
            onPress={checkHealth}
            disabled={isChecking}
            className="border-white"
          >
            <M3EButtonText className="text-white">
              {t('network.retry', '重试')}
            </ButtonText>
          </Button>
        )}
        */}
      </View>
    </View>
  );
};
