import React from 'react';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { UserPlus, Check, MessageCircle } from 'lucide-react-native';
import { M3EButtonFilled, M3EButtonText, M3EIconButton } from '@/components/ui/m3e-button/m3e-buttons';

interface UserActionButtonsProps {
  isFollowing: boolean;
  isFollowingInProgress: boolean;
  onFollowToggle: () => void;
  onSendMessage: () => void;
}

export default function UserActionButtons({
  isFollowing,
  isFollowingInProgress,
  onFollowToggle,
  onSendMessage,
}: UserActionButtonsProps) {
  const { t } = useTranslation();

  return (
    <View className="flex-row p-4 justify-center gap-3">
      <M3EButtonFilled
        action={isFollowing ? 'secondary' : 'primary'}
        variant={isFollowing ? 'outline' : 'solid'}
       
        onPress={onFollowToggle}
        isDisabled={isFollowingInProgress}
        className="flex-1 flex-row items-center justify-center rounded-full py-2"
      >
        {isFollowingInProgress ? (
          <M3EProgressIndicator />
        ) : (
          <>
            <M3EIconButton icon={<isFollowing ? Check : UserPlus size={16}
              className="mr-2"
             />} />
            <M3EButtonText>
              {isFollowing
                ? t('social.userProfile.followingStatus', '已关注')
                : t('social.userProfile.follow', '关注')}
            </M3EButtonText>
          </>
        )}
      </M3EButtonFilled>

      <M3EButtonFilled
        action="secondary"
        variant="outline"
       
        onPress={onSendMessage}
        className="flex-1 flex-row items-center justify-center rounded-full py-2"
      >
        <M3EIconButton icon={<MessageCircle size={16} className="mr-2"  />} />
        <M3EButtonText>{t('social.userProfile.message', '发消息')}</M3EButtonText>
      </M3EButtonFilled>
    </View>
  );
}
