import React, { useState } from 'react';
import { Text } from '@/components/base';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Send } from 'lucide-react-native';

import { TextArea } from '@/components/base';
import { M3EFab } from '@/components/ui/m3e-button/m3e-fab';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

interface MessageInputProps {
  onSend: (message: string) => Promise<void>;
  placeholder?: string;
}

export default function MessageInput({
  onSend,
  placeholder,
}: MessageInputProps) {
  const { t } = useTranslation();
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  const handleSend = async () => {
    if (message.trim() === '' || isSending) {
      return;
    }

    setIsSending(true);
    try {
      await onSend(message);
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const isDisabled = message.trim() === '' || isSending;

  return (
    <View className="flex-row items-center px-4 py-2 border-t border-outline-300 bg-background-0">
      <TextArea
        className="flex-1 min-h-[40px] max-h-[100px] bg-background-100 rounded-md px-4 py-2 mr-4 text-base text-typography-900"
        placeholder={placeholder || t('messages.typeSomething', '输入消息...')}
        value={message}
        onChangeText={setMessage}
        multiline
        maxLength={1000}
        onSubmitEditing={handleSend}
      />

      <M3EFab
        size="small"
        onPress={handleSend}
        disabled={isDisabled}
        icon={
          isSending ? (
            <M3EProgressIndicator size="small" color="white" />
          ) : (
            <Send size={20} color="white" />
          )
        }
        className="w-10 h-10"
      />
    </View>
  );
}
