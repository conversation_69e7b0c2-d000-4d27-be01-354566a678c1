import React from 'react';
import { View } from 'react-native';
import { Pressable, Text } from '@/components/base';
import { useColorScheme } from 'nativewind';

interface TopicChipProps {
  topic: string;
  onPress?: (topic: string) => void;
}

export function TopicChip({ topic, onPress }: TopicChipProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <Pressable className={`px-3 py-1 rounded-full ${
        isDark ? 'bg-primary-800/20' : 'bg-primary-500/20'
      }`}
      onPress={() => onPress?.(topic)}
      disabled={!onPress}
    >
      <Text
        className={`text-xs font-medium ${
          isDark ? 'text-primary-400' : 'text-primary-600'
        }`}
      >
        {topic}
      </Text>
    </Pressable>
  );
}
