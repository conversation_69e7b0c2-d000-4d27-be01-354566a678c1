#!/usr/bin/env node

/**
 * 综合性语法修复脚本
 * 
 * 一次性解决所有已知的语法问题：
 * 1. M3ETextField 使用错误
 * 2. 破损的导入语句
 * 3. 重复导入
 * 4. 不存在的导入
 * 5. 语法错误
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 综合修复函数
const comprehensiveFix = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const issues = [];

    // 1. 修复破损的导入语句
    const brokenImportPattern = /^\s*([A-Z][a-zA-Z_,\s]*),?\s*\}\s*from\s*['"][^'"]*['"];?$/gm;
    const brokenImports = content.match(brokenImportPattern);
    if (brokenImports) {
      for (const brokenImport of brokenImports) {
        if (brokenImport.includes('M3ETextField')) {
          content = content.replace(brokenImport, "import { M3ETextField } from '@/components/ui/m3e-text-field';");
          modified = true;
          issues.push('修复破损的 M3ETextField 导入');
        }
      }
    }

    // 2. 移除不存在的导入
    content = content.replace(/,?\s*M3ETextFieldField\s*,?/g, '');
    if (content !== fs.readFileSync(filePath, 'utf8')) {
      modified = true;
      issues.push('移除不存在的 M3ETextFieldField 导入');
    }

    // 3. 修复重复的 M3EProgressIndicator 导入
    const progressImports = content.match(/import.*M3EProgressIndicator.*from.*$/gm);
    if (progressImports && progressImports.length > 1) {
      // 移除从 m3e-button 中的导入
      content = content.replace(
        /import\s*{\s*([^}]*),\s*M3EProgressIndicator\s*([^}]*)\s*}\s*from\s*['"]@\/components\/ui\/m3e-button\/m3e-buttons['"];?/g,
        (match, before, after) => {
          const cleanBefore = before.trim();
          const cleanAfter = after.trim();
          let imports = [];
          if (cleanBefore) imports.push(cleanBefore);
          if (cleanAfter) imports.push(cleanAfter);
          return `import { ${imports.join(', ')} } from '@/components/ui/m3e-button/m3e-buttons';`;
        }
      );
      
      // 确保有正确的导入
      if (!content.includes("import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator'")) {
        const lastImportMatch = content.match(/^import.*$/gm);
        if (lastImportMatch) {
          const lastImport = lastImportMatch[lastImportMatch.length - 1];
          const insertIndex = content.indexOf(lastImport) + lastImport.length;
          content = content.slice(0, insertIndex) + 
            "\nimport { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';" + 
            content.slice(insertIndex);
        }
      }
      modified = true;
      issues.push('修复重复的 M3EProgressIndicator 导入');
    }

    // 4. 修复 M3ETextField 的错误使用
    const textFieldContainerPattern = /<M3ETextField([^>]*?)>\s*<[^>]*\/>\s*<\/M3ETextField>/gs;
    content = content.replace(textFieldContainerPattern, (match, props) => {
      modified = true;
      issues.push('修复 M3ETextField 容器使用');
      return `<M3ETextField${props} />`;
    });

    // 5. 修复破损的自闭合标签
    content = content.replace(/\s*\/\s*\/>/g, ' />');
    content = content.replace(/\s*\/\s*>/g, ' />');

    // 6. 修复破损的 M3EProgressIndicator 标签
    content = content.replace(
      /<M3EProgressIndicator([^>]*?)\s*\/?\s*>/g,
      '<M3EProgressIndicator$1 />'
    );

    // 7. 清理空的导入语句
    content = content.replace(/import\s*{\s*,?\s*}\s*from\s*['"][^'"]*['"];?\s*\n?/g, '');

    // 8. 修复导入语句中的逗号问题
    content = content.replace(/import\s*{\s*,/g, 'import {');
    content = content.replace(/,\s*,/g, ',');
    content = content.replace(/,\s*}/g, ' }');

    // 9. 清理多余的空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    // 10. 确保正确的 M3ETextField 导入（如果使用了但没有导入）
    if (content.includes('M3ETextField') && !content.includes("import { M3ETextField } from '@/components/ui/m3e-text-field'")) {
      const lastImportMatch = content.match(/^import.*$/gm);
      if (lastImportMatch) {
        const lastImport = lastImportMatch[lastImportMatch.length - 1];
        const insertIndex = content.indexOf(lastImport) + lastImport.length;
        content = content.slice(0, insertIndex) + 
          "\nimport { M3ETextField } from '@/components/ui/m3e-text-field';" + 
          content.slice(insertIndex);
        modified = true;
        issues.push('添加缺失的 M3ETextField 导入');
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      issues.forEach(issue => console.log(`  - ${issue}`));
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始综合性语法修复...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let fixedCount = 0;
  
  for (const file of allFiles) {
    const fixed = comprehensiveFix(file);
    if (fixed) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 修复了 ${fixedCount} 个文件中的语法问题`);
  
  if (fixedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 重新运行应用程序');
    console.log('2. 检查所有组件是否正常工作');
    console.log('3. 如有剩余问题，请手动检查');
  }
};

main();
