import React from 'react';
import { Button, ButtonText } from '@/components/base';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { M3EButtonFilled, M3EButtonText } from '@/components/ui/m3e-button/m3e-buttons';

interface ActionButtonsProps {
  isLoggedIn: boolean;
  showContinueInput: boolean;
  onToggleContinueInput: () => void;
}

export default function ActionButtons({
  isLoggedIn,
  showContinueInput,
  onToggleContinueInput,
}: ActionButtonsProps) {
  const { t } = useTranslation();

  return (
    <View className="flex-row justify-end items-center mt-1 border-t border-outline-200 dark:border-outline-700 pt-2">
      {/* Placeholder for Like Button */}
      <M3EButtonFilled action="secondary" variant="link" className="ml-3">
        <M3EButtonText className="text-primary-600 dark:text-primary-400">
          Like (TODO)
        </M3EButtonText>
      </M3EButtonFilled>

      {/* Placeholder for Comment Button */}
      <M3EButtonFilled action="secondary" variant="link" className="ml-3">
        <M3EButtonText className="text-primary-600 dark:text-primary-400">
          Comment (TODO)
        </M3EButtonText>
      </M3EButtonFilled>

      {/* Continue Button */}
      {isLoggedIn && (
        <M3EButtonFilled
          action="secondary"
          variant="link"
         
          onPress={onToggleContinueInput}
          className="ml-3"
        >
          <M3EButtonText className="text-primary-600 dark:text-primary-400">
            {showContinueInput
              ? t('story.cancelContinue', 'Cancel')
              : t('story.continue', 'Continue Story...')}
          </M3EButtonText>
        </M3EButtonFilled>
      )}
    </View>
  );
}
