#!/usr/bin/env node

/**
 * 优化 M3E 组件主题使用脚本
 *
 * 将 M3E 组件中的硬编码颜色替换为语义化的 CSS 变量类名
 * 基于 NativeWind 最佳实践
 */

const fs = require('fs');
const path = require('path');

// 查找所有 M3E 组件文件
const findM3EComponents = (dir) => {
  const files = [];

  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);

      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          if (
            ![
              'node_modules',
              '.git',
              '.mine',
              '.cursor',
              'dist',
              'build',
              'scripts',
            ].includes(item)
          ) {
            scanDir(fullPath);
          }
        } else if (
          stat.isFile() &&
          item.endsWith('.tsx') &&
          (fullPath.includes('components\\ui\\m3e-') ||
            fullPath.includes('components/ui/m3e-'))
        ) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };

  scanDir(dir);
  return files;
};

// 颜色映射表：硬编码颜色 -> 语义化类名
const colorMappings = [
  // Primary colors
  { from: /#6750A4/gi, to: 'bg-m3-primary-main', type: 'background' },
  { from: /#FFFFFF/gi, to: 'bg-m3-primary-on', type: 'background' },
  { from: /#EADDFF/gi, to: 'bg-m3-primary-container', type: 'background' },
  { from: /#21005D/gi, to: 'bg-m3-primary-on-container', type: 'background' },

  // Secondary colors
  { from: /#625B71/gi, to: 'bg-m3-secondary-main', type: 'background' },
  { from: /#E8DEF8/gi, to: 'bg-m3-secondary-container', type: 'background' },
  { from: /#1D192B/gi, to: 'bg-m3-secondary-on-container', type: 'background' },

  // Surface colors
  { from: /#FEF7FF/gi, to: 'bg-m3-surface-main', type: 'background' },
  { from: /#1D1B20/gi, to: 'bg-m3-surface-on', type: 'background' },
  { from: /#E7E0EC/gi, to: 'bg-m3-surface-variant', type: 'background' },
  { from: /#49454F/gi, to: 'bg-m3-surface-on-variant', type: 'background' },
  { from: /#F3EDF7/gi, to: 'bg-m3-surface-container', type: 'background' },

  // Outline colors
  { from: /#79747E/gi, to: 'border-m3-outline-main', type: 'border' },
  { from: /#CAC4D0/gi, to: 'border-m3-outline-variant', type: 'border' },

  // Error colors
  { from: /#BA1A1A/gi, to: 'bg-m3-error-main', type: 'background' },
  { from: /#FFDAD6/gi, to: 'bg-m3-error-container', type: 'background' },
];

// 优化 M3E 组件
const optimizeM3EComponent = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const issues = [];

    // 1. 确保导入 useUnifiedTheme
    if (
      content.includes('export const M3E') &&
      !content.includes('useUnifiedTheme')
    ) {
      const importMatch = content.match(/^import.*$/gm);
      if (importMatch) {
        const lastImport = importMatch[importMatch.length - 1];
        const insertIndex = content.indexOf(lastImport) + lastImport.length;
        content =
          content.slice(0, insertIndex) +
          "\nimport { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';" +
          content.slice(insertIndex);
        modified = true;
        issues.push('添加 useUnifiedTheme 导入');
      }
    }

    // 2. 替换硬编码颜色为语义化类名
    for (const mapping of colorMappings) {
      // 在 className 属性中替换
      const classNamePattern = new RegExp(`className="([^"]*?)([^"]*?)"`, 'g');
      content = content.replace(
        classNamePattern,
        (match, beforeClass, afterClass) => {
          const fullClass = beforeClass + afterClass;
          if (mapping.from.test(fullClass)) {
            const newClass = fullClass.replace(mapping.from, mapping.to);
            if (newClass !== fullClass) {
              modified = true;
              issues.push(`替换 className 中的硬编码颜色为 ${mapping.to}`);
              return `className="${newClass}"`;
            }
          }
          return match;
        }
      );

      // 在 style 属性中替换
      const stylePattern = new RegExp(
        `(backgroundColor|borderColor|color):\\s*['"]${mapping.from.source}['"]`,
        'gi'
      );
      if (stylePattern.test(content)) {
        content = content.replace(stylePattern, (match, property) => {
          modified = true;
          issues.push(`替换 style 中的硬编码颜色为 CSS 变量`);
          const cssVar = mapping.to
            .replace('bg-', '--color-')
            .replace('border-', '--color-')
            .replace('text-', '--color-');
          return `${property}: 'var(${cssVar})'`;
        });
      }
    }

    // 3. 替换常见的硬编码样式模式
    const stylePatterns = [
      {
        from: /backgroundColor:\s*['"]#[0-9A-Fa-f]{6}['"],?/g,
        to: "backgroundColor: 'var(--color-surface-container)',",
        message: '替换硬编码背景色为表面容器色',
      },
      {
        from: /color:\s*['"]#[0-9A-Fa-f]{6}['"],?/g,
        to: "color: 'var(--color-surface-on)',",
        message: '替换硬编码文字色为表面文字色',
      },
      {
        from: /borderColor:\s*['"]#[0-9A-Fa-f]{6}['"],?/g,
        to: "borderColor: 'var(--color-outline)',",
        message: '替换硬编码边框色为轮廓色',
      },
    ];

    for (const pattern of stylePatterns) {
      if (pattern.from.test(content)) {
        content = content.replace(pattern.from, pattern.to);
        modified = true;
        issues.push(pattern.message);
      }
    }

    // 4. 优化 className 使用语义化类名
    const classOptimizations = [
      {
        from: /className="([^"]*?)bg-blue-\d+([^"]*?)"/g,
        to: 'className="$1bg-m3-primary-main$2"',
        message: '优化蓝色背景为主色背景',
      },
      {
        from: /className="([^"]*?)text-blue-\d+([^"]*?)"/g,
        to: 'className="$1text-m3-primary-main$2"',
        message: '优化蓝色文字为主色文字',
      },
      {
        from: /className="([^"]*?)bg-gray-\d+([^"]*?)"/g,
        to: 'className="$1bg-m3-surface-variant$2"',
        message: '优化灰色背景为表面变体背景',
      },
      {
        from: /className="([^"]*?)text-gray-\d+([^"]*?)"/g,
        to: 'className="$1text-m3-surface-on-variant$2"',
        message: '优化灰色文字为表面变体文字',
      },
      {
        from: /className="([^"]*?)border-gray-\d+([^"]*?)"/g,
        to: 'className="$1border-m3-outline-variant$2"',
        message: '优化灰色边框为轮廓变体边框',
      },
    ];

    for (const optimization of classOptimizations) {
      if (optimization.from.test(content)) {
        content = content.replace(optimization.from, optimization.to);
        modified = true;
        issues.push(optimization.message);
      }
    }

    // 5. 添加动态主题支持（如果组件使用了 useUnifiedTheme）
    if (
      content.includes('useUnifiedTheme') &&
      !content.includes('const { themeVars }')
    ) {
      // 查找组件函数开始位置
      const componentMatch = content.match(/export const (M3E\w+)[^{]*{/);
      if (componentMatch) {
        const componentStart =
          content.indexOf(componentMatch[0]) + componentMatch[0].length;
        const hookInsert =
          '\n  const { themeVars, isDark } = useUnifiedTheme();\n';
        content =
          content.slice(0, componentStart) +
          hookInsert +
          content.slice(componentStart);
        modified = true;
        issues.push('添加主题变量解构');
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已优化: ${filePath}`);
      issues.forEach((issue) => console.log(`  - ${issue}`));
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始优化 M3E 组件主题使用...');

  const m3eFiles = findM3EComponents(process.cwd());
  console.log(`📁 找到 ${m3eFiles.length} 个 M3E 组件文件`);

  let optimizedCount = 0;

  for (const file of m3eFiles) {
    const optimized = optimizeM3EComponent(file);
    if (optimized) {
      optimizedCount++;
    }
  }

  console.log(`\n✨ 完成! 优化了 ${optimizedCount} 个 M3E 组件`);

  if (optimizedCount > 0) {
    console.log('\n📝 下一步建议：');
    console.log('1. 测试所有 M3E 组件的显示效果');
    console.log('2. 验证明暗模式切换是否正常');
    console.log('3. 检查颜色是否符合 M3E 设计规范');
    console.log('4. 手动调整任何需要特殊处理的组件');
  }
};

main();
