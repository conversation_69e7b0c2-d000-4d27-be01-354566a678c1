import React from 'react';
import { View } from 'react-native';
import { Pressable, Text } from '@/components/base';

import { Sparkles } from 'lucide-react-native';

interface AiSuggestionCardProps {
  suggestion: string;
  onSelect: (text: string) => void;
}

export default function AiSuggestionCard({
  suggestion,
  onSelect,
}: AiSuggestionCardProps) {
  return (
    <Pressable className="p-4 rounded-md border border-outline-200 bg-background-50 dark:border-outline-700 dark:bg-background-800"
      onPress={() => onSelect(suggestion)}
    >
      <View className="flex flex-row items-center">
        <Sparkles size={16} className="text-primary-500 mr-2" />
        <Text
          size="md"
          className="font-medium text-typography-900 dark:text-typography-50 flex-1 truncate"
        >
          {suggestion}
        </Text>
      </View>
    </Pressable>
  );
}
