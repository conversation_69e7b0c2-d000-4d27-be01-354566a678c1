import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { M3EButtonFilled, M3EButtonText } from '@/components/ui/m3e-button/m3e-buttons';
import { Text  } from '@/components/base';

interface ProfileScreenAuthProps {
  onLogin: () => void;
  onRegister: () => void;
}

export function ProfileScreenAuth({
  onLogin,
  onRegister,
}: ProfileScreenAuthProps) {
  const { t } = useTranslation();

  return (
    <View className="flex-1 p-6 bg-background-100 justify-center items-center">
      <View className="items-center w-full">
        <Text className="text-center text-lg mb-6">
          {t('profileAuthPrompt')}
        </Text>
        <M3EButtonFilled
          action="primary"
          variant="solid"
         
          onPress={onLogin}
          className="w-4/5 py-3 rounded-xl mb-3 min-h-[48px]"
        >
          <M3EButtonText>{t('loginButton')}</M3EButtonText>
        </M3EButtonFilled>
        <M3EButtonFilled
          action="secondary"
          variant="solid"
         
          onPress={onRegister}
          className="w-4/5 py-3 rounded-xl mb-3 min-h-[48px]"
        >
          <M3EButtonText>{t('registerButton')}</M3EButtonText>
        </M3EButtonFilled>
      </View>
    </View>
  );
}
