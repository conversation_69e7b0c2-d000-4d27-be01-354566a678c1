/**
 * 迁移 Button 兼容层脚本
 * 
 * 将所有使用 Button, ButtonText, ButtonIcon, ButtonSpinner 兼容层的地方
 * 迁移到直接使用 M3E 组件
 */

const fs = require('fs');
const path = require('path');

// 需要迁移的文件列表（从搜索结果中提取）
const filesToMigrate = [
  'app/stories/[id]/comments/index.tsx',
  'components/stories/story-part-node/action-buttons.tsx',
  'components/stories/story-part-node/continuation-input.tsx',
  'features/auth/screens/login-screen.tsx',
  'features/auth/screens/new-password-screen.tsx',
  'features/auth/screens/register-screen.tsx',
  'features/auth/screens/reset-password-screen.tsx',
  'features/comments/components/comment-input.tsx',
  'features/comments/components/comment-item.tsx',
  'features/creation/components/aisuggestions-section/index.tsx',
  'features/creation/components/create-story-form/index.tsx',
  'features/creation/components/segment-form.tsx',
  'features/creation/screens/create-segment-screen.tsx',
  'features/home/<USER>/home-screen-error.tsx',
  'features/messages/components/messages-tab/index.tsx',
  'features/messages/screens/new-conversation-screen/index.tsx',
  'features/profile/components/profile-form/index.tsx',
  'features/profile/components/profile-screen-auth.tsx',
  'features/settings/screens/settings-screen.tsx',
  'features/social/components/user-action-buttons/index.tsx',
  'features/social/components/placeholder-tab.tsx',
  'features/stories/components/branch-actions-menu/index.tsx',
  'features/stories/components/branch-carousel/index.tsx',
  'features/stories/components/branch-comments/index.tsx',
  'features/stories/components/branch-manager/index.tsx',
  'features/stories/components/branch-manager/visualization-style-selector.tsx',
  'features/stories/components/branch-visualizer/index.tsx',
  'features/stories/components/branch-votes/index.tsx',
  'features/stories/components/create-branch-form/index.tsx',
  'features/stories/components/story-node-card/index.tsx',
  'features/stories/components/add-segment-form.tsx',
  'features/stories/components/aisuggestion-block.tsx',
  'features/stories/components/empty-stories-state.tsx',
  'features/stories/components/story-detail-header.tsx',
  'features/stories/components/story-header.tsx',
  'features/stories/screens/story-detail-screen/error-state.tsx',
  'features/stories/screens/story-feed-screen/index.tsx',
  'features/test/components/performance-metrics.tsx',
];

// 导入映射规则
const importMappings = [
  {
    // 单独导入 Button 组件
    pattern: /import\s*{\s*Button\s*}\s*from\s*['"]@\/components\/ui\/m3e-button['"];?/g,
    replacement: "import { M3EButtonFilled } from '@/components/ui/m3e-button/m3e-buttons';"
  },
  {
    // 导入多个 Button 组件
    pattern: /import\s*{\s*([^}]*(?:Button|ButtonText|ButtonIcon|ButtonSpinner)[^}]*)\s*}\s*from\s*['"]@\/components\/ui\/m3e-button['"];?/g,
    replacement: (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      const m3eImports = [];
      
      importList.forEach(imp => {
        if (imp.includes('Button') && !imp.includes('ButtonText') && !imp.includes('ButtonIcon') && !imp.includes('ButtonSpinner')) {
          m3eImports.push('M3EButtonFilled');
        } else if (imp.includes('ButtonText')) {
          m3eImports.push('M3EButtonText');
        } else if (imp.includes('ButtonIcon')) {
          // ButtonIcon 通常用于图标，可以用 M3EIconButton 或直接用图标
          m3eImports.push('M3EIconButton');
        } else if (imp.includes('ButtonSpinner')) {
          // ButtonSpinner 可以用 M3EProgressIndicator 替代
          m3eImports.push('M3EProgressIndicator');
        }
      });
      
      const uniqueImports = [...new Set(m3eImports)];
      return `import { ${uniqueImports.join(', ')} } from '@/components/ui/m3e-button/m3e-buttons';`;
    }
  }
];

// 使用映射规则
const usageMappings = [
  {
    // Button 组件使用
    pattern: /<Button\b/g,
    replacement: '<M3EButtonFilled'
  },
  {
    pattern: /<\/Button>/g,
    replacement: '</M3EButtonFilled>'
  },
  {
    // ButtonText 组件使用
    pattern: /<ButtonText\b/g,
    replacement: '<M3EButtonText'
  },
  {
    pattern: /<\/ButtonText>/g,
    replacement: '</M3EButtonText>'
  },
  {
    // ButtonIcon 组件使用 - 需要特殊处理
    pattern: /<ButtonIcon\s+as={([^}]+)}\s*([^>]*)\s*\/>/g,
    replacement: (match, iconComponent, props) => {
      // 提取图标组件和属性
      return `<M3EIconButton icon={<${iconComponent} ${props} />} />`;
    }
  },
  {
    // ButtonSpinner 组件使用
    pattern: /<ButtonSpinner\b/g,
    replacement: '<M3EProgressIndicator'
  },
  {
    pattern: /<\/ButtonSpinner>/g,
    replacement: '</M3EProgressIndicator>'
  }
];

// 处理单个文件
function migrateFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 应用导入映射
    importMappings.forEach(({ pattern, replacement }) => {
      if (typeof replacement === 'function') {
        content = content.replace(pattern, replacement);
      } else {
        if (pattern.test(content)) {
          content = content.replace(pattern, replacement);
          modified = true;
        }
      }
    });

    // 应用使用映射
    usageMappings.forEach(({ pattern, replacement }) => {
      if (typeof replacement === 'function') {
        content = content.replace(pattern, replacement);
      } else {
        if (pattern.test(content)) {
          content = content.replace(pattern, replacement);
          modified = true;
        }
      }
    });

    // 添加必要的导入
    if (content.includes('M3EProgressIndicator') && !content.includes("from '@/components/ui/m3e-progress-indicator'")) {
      const firstImportMatch = content.match(/^import.*$/m);
      if (firstImportMatch) {
        const insertIndex = content.indexOf(firstImportMatch[0]) + firstImportMatch[0].length;
        content = content.slice(0, insertIndex) + 
          `\nimport { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';` + 
          content.slice(insertIndex);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已迁移: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  无需修改: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🚀 开始迁移 Button 兼容层...\n');
  
  let totalFiles = 0;
  let migratedFiles = 0;

  filesToMigrate.forEach(filePath => {
    totalFiles++;
    if (migrateFile(filePath)) {
      migratedFiles++;
    }
  });

  console.log(`\n📊 迁移完成:`);
  console.log(`   总文件数: ${totalFiles}`);
  console.log(`   已迁移: ${migratedFiles}`);
  console.log(`   未修改: ${totalFiles - migratedFiles}`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { migrateFile, filesToMigrate };
