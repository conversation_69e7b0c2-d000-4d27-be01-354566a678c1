/**
 * M3E 演示页面 - 展示新的统一主题系统和 M3E 文字系统
 */

import React from 'react';
import { View, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';
import {
  M3EText,
  M3EDisplayLarge,
  M3EDisplayMedium,
  M3EDisplaySmall,
  M3EHeadlineLarge,
  M3EHeadlineMedium,
  M3EHeadlineSmall,
  M3ETitleLarge,
  M3ETitleMedium,
  M3ETitleSmall,
  M3ELabelLarge,
  M3ELabelMedium,
  M3ELabelSmall,
  M3EBodyLarge,
  M3EBodyMedium,
  M3EBodySmall,
  M3EBodyLargeEmphasized,
  M3EBodyMediumEmphasized,
  M3EBodySmallEmphasized,
  M3EPageTitle,
  M3ECardTitle,
  M3EButtonText,
  M3ESupportingText,
} from '@/components/ui/m3e-typography';
import { M3ECard } from '@/components/ui/m3e-card';

export function M3EDemoPage() {
  const { t } = useTranslation();
  const { colors, isDark, userMode, toggleTheme } = useUnifiedTheme();

  return (
    <ScrollView className="flex-1 bg-m3-background-main p-4 gap-6">
      {/* 页面标题 */}
      <View style={{ alignItems: 'center', marginBottom: 16 }}>
        <M3EDisplayMedium>M3E 设计系统演示</M3EDisplayMedium>
        <M3EBodyMedium color={colors.onSurfaceVariant} style={{ marginTop: 8 }}>
          基于 Figma Material Design 3 Expressive 规范
        </M3EBodyMedium>
      </View>

      {/* 主题信息卡片 */}
      <M3ECard
        variant="elevated"
        title="当前主题信息"
        supportingText={`主题模式: ${mode} | 深色模式: ${isDark ? '是' : '否'}`}
        primaryAction={{
          label: '切换主题',
          onPress: toggleTheme,
        }}
      />

      {/* Display 文字样式 */}
      <M3ECard variant="outlined" title="Display 文字样式">
        <View style={{ gap: 16 }}>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Display Large
            </M3ELabelSmall>
            <M3EDisplayLarge>大标题文字</M3EDisplayLarge>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Display Medium
            </M3ELabelSmall>
            <M3EDisplayMedium>中等大标题</M3EDisplayMedium>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Display Small
            </M3ELabelSmall>
            <M3EDisplaySmall>小标题文字</M3EDisplaySmall>
          </View>
        </View>
      </M3ECard>

      {/* Headline 文字样式 */}
      <M3ECard variant="filled" title="Headline 文字样式">
        <View style={{ gap: 12 }}>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Headline Large
            </M3ELabelSmall>
            <M3EHeadlineLarge>大标题</M3EHeadlineLarge>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Headline Medium
            </M3ELabelSmall>
            <M3EHeadlineMedium>中等标题</M3EHeadlineMedium>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Headline Small
            </M3ELabelSmall>
            <M3EHeadlineSmall>小标题</M3EHeadlineSmall>
          </View>
        </View>
      </M3ECard>

      {/* Title 文字样式 */}
      <M3ECard variant="elevated" title="Title 文字样式">
        <View style={{ gap: 8 }}>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Title Large
            </M3ELabelSmall>
            <M3ETitleLarge>大标题</M3ETitleLarge>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Title Medium
            </M3ELabelSmall>
            <M3ETitleMedium>中等标题</M3ETitleMedium>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Title Small
            </M3ELabelSmall>
            <M3ETitleSmall>小标题</M3ETitleSmall>
          </View>
        </View>
      </M3ECard>

      {/* Body 文字样式 */}
      <M3ECard variant="outlined" title="Body 文字样式">
        <View style={{ gap: 12 }}>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Body Large
            </M3ELabelSmall>
            <M3EBodyLarge>
              这是大号正文文字。适用于较长的文本内容，提供良好的可读性。
            </M3EBodyLarge>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Body Medium
            </M3ELabelSmall>
            <M3EBodyMedium>
              这是中等正文文字。最常用的文字大小，适合大部分内容。
            </M3EBodyMedium>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Body Small
            </M3ELabelSmall>
            <M3EBodySmall>
              这是小号正文文字。适用于辅助信息和说明文字。
            </M3EBodySmall>
          </View>
        </View>
      </M3ECard>

      {/* Body Emphasized 文字样式 */}
      <M3ECard variant="filled" title="Body Emphasized 文字样式">
        <View style={{ gap: 12 }}>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Body Large Emphasized
            </M3ELabelSmall>
            <M3EBodyLargeEmphasized>
              这是强调的大号正文文字。用于需要突出的重要内容。
            </M3EBodyLargeEmphasized>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Body Medium Emphasized
            </M3ELabelSmall>
            <M3EBodyMediumEmphasized>
              这是强调的中等正文文字。用于重要的说明文字。
            </M3EBodyMediumEmphasized>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Body Small Emphasized
            </M3ELabelSmall>
            <M3EBodySmallEmphasized>
              这是强调的小号正文文字。用于重要的辅助信息。
            </M3EBodySmallEmphasized>
          </View>
        </View>
      </M3ECard>

      {/* Label 文字样式 */}
      <M3ECard variant="elevated" title="Label 文字样式">
        <View style={{ gap: 8 }}>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Label Large
            </M3ELabelSmall>
            <M3ELabelLarge>大标签文字</M3ELabelLarge>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Label Medium
            </M3ELabelSmall>
            <M3ELabelMedium>中等标签文字</M3ELabelMedium>
          </View>
          <View>
            <M3ELabelSmall color={colors.onSurfaceVariant}>
              Label Small
            </M3ELabelSmall>
            <M3ELabelSmall>小标签文字</M3ELabelSmall>
          </View>
        </View>
      </M3ECard>

      {/* 颜色演示 */}
      <M3ECard variant="outlined" title="主题颜色演示">
        <View style={{ gap: 8 }}>
          <M3EBodyMedium color={colors.primary}>Primary Color</M3EBodyMedium>
          <M3EBodyMedium color={colors.secondary}>
            Secondary Color
          </M3EBodyMedium>
          <M3EBodyMedium color={colors.tertiary}>Tertiary Color</M3EBodyMedium>
          <M3EBodyMedium color={colors.error}>Error Color</M3EBodyMedium>
          <M3EBodyMedium color={colors.onSurface}>On Surface</M3EBodyMedium>
          <M3EBodyMedium color={colors.onSurfaceVariant}>
            On Surface Variant
          </M3EBodyMedium>
        </View>
      </M3ECard>

      <View style={{ height: 32 }} />
    </ScrollView>
  );
}
