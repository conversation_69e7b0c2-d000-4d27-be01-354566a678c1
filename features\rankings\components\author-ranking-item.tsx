import React from 'react';
import { View } from 'react-native';
import { Profile } from '@/api/profiles';
import { useTranslation } from 'react-i18next';
import { BookOpen, Users } from 'lucide-react-native';

import { Text, Pressable } from '@/components/base';
import { Avatar } from '@/components/ui/avatar';



interface AuthorRankingItemProps {
  author: Profile;
  rank: number;
  onPress: (authorId: string) => void;
}

export function AuthorRankingItem({
  author,
  rank,
  onPress,
}: AuthorRankingItemProps) {
  const { t } = useTranslation();

  const handlePress = () => {
    onPress(author.id);
  };

  // Get rank color
  const getRankColor = () => {
    switch (rank) {
      case 1:
        return 'text-amber-500 dark:text-amber-400';
      case 2:
        return 'text-slate-400 dark:text-slate-300';
      case 3:
        return 'text-amber-700 dark:text-amber-600';
      default:
        return 'text-typography-500 dark:text-typography-400';
    }
  };

  return (
    <Pressable className="flex-row p-4 bg-background-100 dark:bg-background-800 rounded-lg mb-4 items-center"
      onPress={handlePress}
    >
      <View className="w-[30px] items-center justify-center mr-2">
        <Text className={`font-bold text-lg ${getRankColor()}`}>
          {rank}
        </Text>
      </View>

      <View className="mr-4">
        <Avatar
          size={60}
          uri={author.avatar_url || ''}
          username={author.username || ''}
        />
      </View>

      <View className="flex flex-col flex-1 justify-between">
        <Text 
          className="font-medium text-base text-typography-900 dark:text-typography-100 mb-0.5"
          numberOfLines={1}
        >
          {author.username || t('rankings.unknownAuthor', '未知作者')}
        </Text>

        <Text 
          className="text-sm text-typography-500 dark:text-typography-400 mb-0.5"
          numberOfLines={1}
        >
          {author.full_name || ''}
        </Text>

        <View className="flex flex-row items-center">
          <View className="flex flex-row items-center mr-4">
            <BookOpen size={14} className="text-typography-500 dark:text-typography-400" />
            <Text className="text-xs text-typography-500 dark:text-typography-400 ml-1">
              {author.stories_count || 0} {t('rankings.stories', '故事')}
            </Text>
          </View>

          <View className="flex flex-row items-center">
            <Users size={14} className="text-typography-500 dark:text-typography-400" />
            <Text className="text-xs text-typography-500 dark:text-typography-400 ml-1">
              {author.followers_count || 0} {t('rankings.followers', '粉丝')}
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
}
