#!/usr/bin/env node

/**
 * 统一主题系统脚本
 * 
 * 解决主题提供者混乱问题：
 * 1. 统一所有文件使用 useUnifiedTheme from unified-theme-provider
 * 2. 移除其他主题提供者的使用
 * 3. 确保主题系统集中化
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 统一主题导入
const unifyThemeImports = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const issues = [];

    // 1. 替换所有错误的主题提供者导入
    const wrongImports = [
      {
        pattern: /import\s*{\s*([^}]*useUnifiedTheme[^}]*)\s*}\s*from\s*['"]@\/lib\/theme\/nativewind-theme-provider['"];?/g,
        replacement: "import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';",
        message: '替换 nativewind-theme-provider 导入'
      },
      {
        pattern: /import\s*{\s*([^}]*useM3ETheme[^}]*)\s*}\s*from\s*['"]@\/lib\/theme\/nativewind-m3e-provider['"];?/g,
        replacement: "import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';",
        message: '替换 nativewind-m3e-provider 导入'
      },
      {
        pattern: /import\s*{\s*([^}]*useTheme[^}]*)\s*}\s*from\s*['"]@\/lib\/theme\/theme-provider['"];?/g,
        replacement: "import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';",
        message: '替换 theme-provider 导入'
      },
      {
        pattern: /import\s*{\s*([^}]*useAppTheme[^}]*)\s*}\s*from\s*['"][^'"]*['"];?/g,
        replacement: "import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';",
        message: '替换 useAppTheme 导入'
      }
    ];

    for (const { pattern, replacement, message } of wrongImports) {
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement);
        modified = true;
        issues.push(message);
      }
    }

    // 2. 替换 Hook 使用
    const hookReplacements = [
      { from: /\buseM3ETheme\(\)/g, to: 'useUnifiedTheme()', message: '替换 useM3ETheme() 调用' },
      { from: /\buseTheme\(\)/g, to: 'useUnifiedTheme()', message: '替换 useTheme() 调用' },
      { from: /\buseAppTheme\(\)/g, to: 'useUnifiedTheme()', message: '替换 useAppTheme() 调用' }
    ];

    for (const { from, to, message } of hookReplacements) {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
        issues.push(message);
      }
    }

    // 3. 移除重复的导入
    const importLines = content.match(/^import.*useUnifiedTheme.*$/gm) || [];
    if (importLines.length > 1) {
      // 保留第一个，移除其他的
      for (let i = 1; i < importLines.length; i++) {
        content = content.replace(importLines[i], '');
        modified = true;
        issues.push('移除重复的 useUnifiedTheme 导入');
      }
    }

    // 4. 清理空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已统一: ${filePath}`);
      issues.forEach(issue => console.log(`  - ${issue}`));
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 检查并报告主题系统状态
const checkThemeSystemStatus = () => {
  console.log('\n🔍 检查主题系统状态...');
  
  // 检查主题提供者文件
  const themeProviders = [
    'lib/theme/unified-theme-provider.tsx',
    'lib/theme/nativewind-theme-provider.tsx',
    'lib/theme/nativewind-m3e-provider.tsx',
    'lib/theme/theme-provider.tsx'
  ];
  
  const existingProviders = themeProviders.filter(provider => {
    try {
      return fs.existsSync(provider);
    } catch {
      return false;
    }
  });
  
  console.log(`📁 发现的主题提供者文件: ${existingProviders.length}`);
  existingProviders.forEach(provider => console.log(`  - ${provider}`));
  
  if (existingProviders.length > 1) {
    console.log('⚠️  警告: 发现多个主题提供者文件，建议只保留 unified-theme-provider.tsx');
  }
  
  // 检查主题配置文件
  const themeConfigs = [
    'lib/theme/unified-theme-config.ts',
    'lib/theme/nativewind-m3e-theme.ts',
    'lib/theme/themes.ts'
  ];
  
  const existingConfigs = themeConfigs.filter(config => {
    try {
      return fs.existsSync(config);
    } catch {
      return false;
    }
  });
  
  console.log(`📁 发现的主题配置文件: ${existingConfigs.length}`);
  existingConfigs.forEach(config => console.log(`  - ${config}`));
};

// 主函数
const main = () => {
  console.log('🚀 开始统一主题系统...');
  
  // 检查当前状态
  checkThemeSystemStatus();
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`\n📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let unifiedCount = 0;
  
  for (const file of allFiles) {
    const unified = unifyThemeImports(file);
    if (unified) {
      unifiedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 统一了 ${unifiedCount} 个文件的主题导入`);
  
  if (unifiedCount > 0) {
    console.log('\n📝 下一步建议：');
    console.log('1. 测试应用启动是否正常');
    console.log('2. 验证主题切换功能');
    console.log('3. 考虑移除不再使用的主题提供者文件');
    console.log('4. 确保所有组件使用统一的主题系统');
  }
  
  // 再次检查状态
  console.log('\n' + '='.repeat(50));
  checkThemeSystemStatus();
};

main();
