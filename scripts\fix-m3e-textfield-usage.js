#!/usr/bin/env node

/**
 * 修复 M3ETextField 组件的错误使用
 * 
 * 问题：M3ETextField 被当作容器组件使用，但它应该是自闭合的
 * 解决：将 <M3ETextField ...><TextInput .../></M3ETextField> 转换为 <M3ETextField .../>
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 修复文件中的 M3ETextField 使用
const fixM3ETextFieldUsage = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 检查是否包含 M3ETextField
    if (!content.includes('M3ETextField')) {
      return false;
    }

    // 模式1: <M3ETextField ...><TextInput .../></M3ETextField>
    // 这种情况下，移除内部的 TextInput 并将 M3ETextField 改为自闭合
    const pattern1 = /<M3ETextField([^>]*?)>\s*<TextInput([^>]*?)\/>\s*<\/M3ETextField>/gs;
    content = content.replace(pattern1, (match, m3eProps, textInputProps) => {
      modified = true;
      console.log(`  修复模式1: M3ETextField 包含 TextInput`);
      // 合并属性，优先使用 TextInput 的属性
      return `<M3ETextField${m3eProps}${textInputProps} />`;
    });

    // 模式2: <M3ETextField ...>\s*<M3ETextField .../>\s*</M3ETextField>
    // 这种情况下，移除外层的 M3ETextField 标签
    const pattern2 = /<M3ETextField([^>]*?)>\s*<M3ETextField([^>]*?)\/>\s*<\/M3ETextField>/gs;
    content = content.replace(pattern2, (match, outerProps, innerProps) => {
      modified = true;
      console.log(`  修复模式2: 嵌套的 M3ETextField`);
      // 合并属性，优先使用内层的属性
      return `<M3ETextField${outerProps}${innerProps} />`;
    });

    // 模式3: <M3ETextField ...>\s*任何内容\s*</M3ETextField>
    // 这种情况下，将其转换为自闭合，并移除内容
    const pattern3 = /<M3ETextField([^>]*?)>\s*([^<]*?)\s*<\/M3ETextField>/gs;
    content = content.replace(pattern3, (match, props, innerContent) => {
      // 如果内容只是空白字符，则直接转换为自闭合
      if (innerContent.trim() === '') {
        modified = true;
        console.log(`  修复模式3: M3ETextField 包含空内容`);
        return `<M3ETextField${props} />`;
      }
      return match; // 保持不变，可能需要手动处理
    });

    // 模式4: 修复属性中的错误
    // 移除 className 中的输入框样式，因为 M3ETextField 有自己的样式
    const pattern4 = /(M3ETextField[^>]*?)className="([^"]*?)"/g;
    content = content.replace(pattern4, (match, beforeClass, classValue) => {
      // 移除输入框相关的样式类
      const cleanedClass = classValue
        .replace(/\bp-\d+\b/g, '') // 移除 padding
        .replace(/\bborder\b/g, '') // 移除 border
        .replace(/\bborder-\w+-\d+\b/g, '') // 移除 border-color
        .replace(/\brounded-\w+\b/g, '') // 移除 rounded
        .replace(/\bbg-\w+-\d+\b/g, '') // 移除 background
        .replace(/\btext-\w+-\d+\b/g, '') // 移除 text-color
        .replace(/\s+/g, ' ') // 清理多余空格
        .trim();
      
      if (cleanedClass !== classValue) {
        modified = true;
        console.log(`  修复模式4: 清理 M3ETextField 的 className`);
        if (cleanedClass) {
          return `${beforeClass}className="${cleanedClass}"`;
        } else {
          return beforeClass.trim();
        }
      }
      return match;
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复 M3ETextField 组件的错误使用...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let fixedCount = 0;
  
  for (const file of allFiles) {
    const fixed = fixM3ETextFieldUsage(file);
    if (fixed) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 修复了 ${fixedCount} 个文件中的 M3ETextField 使用问题`);
  
  if (fixedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 运行应用程序测试所有表单功能');
    console.log('2. 检查 M3ETextField 是否正确显示');
    console.log('3. 验证输入框的交互功能正常');
    console.log('4. 如有需要，手动调整剩余的复杂情况');
  }
};

main();
