/**
 * M3E 演示模态框
 */

import React from 'react';
import { Modal, View, Pressable } from 'react-native';
import { X } from 'lucide-react-native';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';
import { M3EText } from '@/components/ui/m3e-typography';
import { M3EDemoPage } from '@/features/settings/components/m3e-demo-page';

interface M3EDemoModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function M3EDemoModal({ visible, onClose }: M3EDemoModalProps) {
  const { colors } = useUnifiedTheme();

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-m3-background-main">
        {/* Header */}
        <View className="flex-row items-center justify-between px-4 py-3 border-b border-m3-outline-variant bg-m3-surface-main">
          <M3EText variant="titleLarge" className="text-m3-surface-on">
            M3E 设计系统演示
          </M3EText>
          <Pressable
            onPress={onClose}
            className="p-2 rounded-full bg-m3-surface-container"
          >
            <X size={20} className="text-m3-surface-on" />
          </Pressable>
        </View>

        {/* Content */}
        <M3EDemoPage />
      </View>
    </Modal>
  );
}
