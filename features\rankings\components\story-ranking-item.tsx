import React from 'react';
import { Image , View } from 'react-native';
import { Story } from '@/api/stories';
import { useTranslation } from 'react-i18next';
import { formatDistanceToNow } from 'date-fns';
import { Heart } from 'lucide-react-native';

import { Text, Pressable } from '@/components/base';



interface StoryRankingItemProps {
  story: Story;
  rank: number;
  onPress: (storyId: string) => void;
}

export function StoryRankingItem({
  story,
  rank,
  onPress,
}: StoryRankingItemProps) {
  const { t } = useTranslation();

  const formattedDate = story.updated_at
    ? formatDistanceToNow(new Date(story.updated_at), { addSuffix: true })
    : '';

  const handlePress = () => {
    onPress(story.id);
  };

  // Get rank color
  const getRankColor = () => {
    switch (rank) {
      case 1:
        return 'text-amber-500 dark:text-amber-400';
      case 2:
        return 'text-slate-400 dark:text-slate-300';
      case 3:
        return 'text-amber-700 dark:text-amber-600';
      default:
        return 'text-typography-500 dark:text-typography-400';
    }
  };

  return (
    <Pressable className="flex-row p-4 bg-background-100 dark:bg-background-800 rounded-lg mb-4 items-center"
      onPress={handlePress}
    >
      <View className="w-[30px] items-center justify-center mr-2">
        <Text className={`font-bold text-lg ${getRankColor()}`}>
          {rank}
        </Text>
      </View>

      <View className="mr-4">
        {story.cover_image_url ? (
          <Image
            source={{ uri: story.cover_image_url }}
            style={{
              width: 60,
              height: 60,
              borderRadius: 4,
            }}
            resizeMode="cover"
          />
        ) : (
          <View className="w-[60px] h-[60px] rounded bg-outline-200 dark:bg-outline-700" />
        )}
      </View>

      <View className="flex flex-col flex-1 justify-between">
        <Text 
          className="font-medium text-base text-typography-900 dark:text-typography-100 mb-0.5"
          numberOfLines={1}
        >
          {story.title}
        </Text>

        <View className="flex flex-row items-center mb-0.5">
          <Text 
            className="text-sm text-typography-500 dark:text-typography-400 mr-2"
            numberOfLines={1}
          >
            {story.profiles?.username || t('rankings.unknownAuthor', '未知作者')}
          </Text>
          <Text className="text-xs text-typography-400 dark:text-typography-500">
            {formattedDate}
          </Text>
        </View>

        <View className="flex flex-row items-center">
          <View className="flex flex-row items-center">
            <Heart size={14} className="text-primary-500 dark:text-primary-400" />
            <Text className="text-xs text-typography-500 dark:text-typography-400 ml-1">
              {story.likes_count || 0}
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
}
