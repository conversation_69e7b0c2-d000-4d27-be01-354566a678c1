import React from 'react';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { View, Image } from 'react-native';
import { useTranslation } from 'react-i18next';
import { StoryWithSegments } from '@/api/stories';
import { Heart } from 'lucide-react-native';

// Import gluestack-ui components

import { Text  } from '@/components/base';

import { M3EButtonFilled, M3EButtonText } from '@/components/ui/m3e-button/m3e-buttons';
import { M3EIconButton } from '@/components/ui/m3e-button/m3e-buttons';

interface StoryDetailHeaderProps {
  story: StoryWithSegments; // Use the full story object for now, can be optimized later
  isLiked: boolean;
  likeCount: number;
  onLikeToggle: () => void;
  isLiking: boolean;
}

export default function StoryDetailHeader({
  story,
  isLiked,
  likeCount,
  onLikeToggle,
  isLiking,
}: StoryDetailHeaderProps) {
  const { t } = useTranslation();

  if (!story) return null; // Should not happen if parent component handles loading

  return (
    <View className="flex flex-col items-center mb-6">
      {story.cover_image_url && (
        <Image
          source={{ uri: story.cover_image_url }}
          alt={story.title}
          className="w-full h-64 rounded-xl mb-4 bg-surface-100 dark:bg-surface-800"
          resizeMode="cover"
        />
      )}
      <Text className="text-2xl font-bold text-typography-900 dark:text-typography-100 text-center mb-1">
        {story.title}
      </Text>
      <Text className="text-base text-typography-700 dark:text-typography-300 mb-3">
        {t('storyDetail.by', 'By')}{' '}
        {story.profiles?.username ||
          t('storyDetail.unknownAuthor', 'Unknown Author')}
      </Text>

      {story.tags && story.tags.length > 0 && (
        <View className="flex flex-row flex-wrap justify-center gap-2 mb-4">
          {story.tags.map((tag, index) => (
            <View key={index}
              className="px-2 py-1 rounded-md bg-primary-100 dark:bg-primary-900"
            >
              <Text className="text-xs text-primary-600 dark:text-primary-400">
                {tag}
              </Text>
            </View>
          ))}
        </View>
      )}

      <View className="mt-2">
        <M3EButtonFilled
          onPress={onLikeToggle}
          isDisabled={isLiking}
          variant={isLiked ? 'solid' : 'outline'}
          action={isLiked ? 'primary' : 'secondary'}
          className="flex-row items-center px-4 py-2 rounded-full"
        >
          <M3EIconButton icon={<Heart size="sm"
            fill={isLiked ? 'currentColor' : 'none'}
           />} />
          <M3EButtonText className="ml-1">
            {likeCount}{' '}
            {likeCount === 1
              ? t('storyDetail.like', 'Like')
              : t('storyDetail.likes', 'Likes')}
          </M3EButtonText>
          {isLiking && <M3EProgressIndicator className="ml-1" />}
        </M3EButtonFilled>
      </View>
    </View>
  );
}
