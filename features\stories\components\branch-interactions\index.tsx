import React, { useState } from 'react';
import { View } from 'react-native';

import { Text, Pressable } from '@/components/base';


import { useTranslation } from 'react-i18next';
import {
  MessageCircle,
  ThumbsUp,
  ChevronDown,
  ChevronUp,
} from 'lucide-react-native';
import { useBranchInteractions } from '../../hooks/use-branch-interactions';
import BranchVotes from '../branch-votes';
import BranchComments from '../branch-comments';
import { useColorScheme } from 'nativewind';

interface BranchInteractionsProps {
  segmentId?: string;
}

export default function BranchInteractions({
  segmentId,
}: BranchInteractionsProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // States
  const [activeTab, setActiveTab] = useState<'votes' | 'comments'>('votes');
  const [isExpanded, setIsExpanded] = useState(false);

  // Use branch interactions hook
  const {
    comments,
    voteStats,
    userVote,
    isLoadingComments,
    isLoadingVotes,
    isAddingComment,
    isVoting,
    addComment,
    deleteComment,
    vote,
    refreshComments,
    refreshVotes,
  } = useBranchInteractions({ segmentId });

  // Toggle expanded/collapsed
  const toggleExpanded = () => {
    setIsExpanded((prev) => !prev);
  };

  // Render tabs
  const renderTabs = () => {
    return (
      <View className="flex flex-row mb-4 rounded-md bg-surface-50 dark:bg-surface-900 border border-outline-200 dark:border-outline-700 overflow-hidden">
        <Pressable className={`flex-1 flex-row items-center justify-center py-2 px-4 ${
            activeTab === 'votes' ? 'bg-primary-100 dark:bg-primary-900' : ''
          }`}
          onPress={() => setActiveTab('votes')}
        >
          <ThumbsUp
            size={16}
            color={
              activeTab === 'votes'
                ? '#6366F1' // primary-500
                : isDark
                ? '#E4E4E7'
                : '#27272A' // text color
            }
          />
          <Text
            className={`text-sm ml-1 ${
              activeTab === 'votes'
                ? 'text-primary-500 font-bold'
                : 'text-typography-900 dark:text-typography-50 font-medium'
            }`}
          >
            {t('storyDetail.votes', 'Votes')}
          </Text>
        </Pressable>

        <Pressable className={`flex-1 flex-row items-center justify-center py-2 px-4 ${
            activeTab === 'comments' ? 'bg-primary-100 dark:bg-primary-900' : ''
          }`}
          onPress={() => setActiveTab('comments')}
        >
          <MessageCircle
            size={16}
            color={
              activeTab === 'comments'
                ? '#6366F1' // primary-500
                : isDark
                ? '#E4E4E7'
                : '#27272A' // text color
            }
          />
          <Text
            className={`text-sm ml-1 ${
              activeTab === 'comments'
                ? 'text-primary-500 font-bold'
                : 'text-typography-900 dark:text-typography-50 font-medium'
            }`}
          >
            {t('storyDetail.comments', 'Comments')} ({comments.length})
          </Text>
        </Pressable>
      </View>
    );
  };

  // Render content
  const renderContent = () => {
    if (activeTab === 'votes') {
      return (
        <BranchVotes
          voteStats={voteStats}
          userVote={userVote}
          isLoading={isLoadingVotes}
          isVoting={isVoting}
          onVote={vote}
        />
      );
    } else {
      return (
        <BranchComments
          comments={comments}
          isLoading={isLoadingComments}
          isAddingComment={isAddingComment}
          onAddComment={addComment}
          onDeleteComment={deleteComment}
          onRefresh={refreshComments}
        />
      );
    }
  };

  if (!segmentId) {
    return null;
  }

  return (
    <View className="bg-background-50 dark:bg-background-950 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
      <Pressable className={`flex-row justify-between items-center p-4 bg-surface-50 dark:bg-surface-900 ${
          isExpanded
            ? 'border-b border-outline-200 dark:border-outline-700'
            : ''
        }`}
        onPress={toggleExpanded}
      >
        <View className="flex flex-row items-center">
          <Text className="text-lg font-bold text-typography-900 dark:text-typography-50 ml-2">
            {t('storyDetail.branchInteractions', 'Branch Interactions')}
          </Text>
        </View>

        {isExpanded ? (
          <ChevronUp size={24} color={isDark ? '#E4E4E7' : '#27272A'} />
        ) : (
          <ChevronDown size={24} color={isDark ? '#E4E4E7' : '#27272A'} />
        )}
      </Pressable>

      {isExpanded && (
        <View className="p-4">
          {renderTabs()}
          {renderContent()}
        </View>
      )}
    </View>
  );
}
