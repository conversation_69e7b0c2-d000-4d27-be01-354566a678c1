import React, { useState, useRef } from 'react';
import { FlatList, RefreshControl, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import StoryNodeCard from '../story-node-card';
import BranchCarousel from '../branch-carousel';
import { useBranchCarousel } from '../../hooks/use-branch-carousel';
import { StorySegment } from '@/api/stories/types';
import { Plus } from 'lucide-react-native';
import { getChildrenCount } from '@/utils/story-helpers';

// Import gluestack-ui components

import { Text  } from '@/components/base';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

// Import M3E components
import { M3EButtonFilled, M3EButtonText, M3EButtonOutlined, M3EButtonTonal, M3EButtonElevated } from '@/components/ui/m3e-button/m3e-buttons';

interface StoryFeedProps {
  segments: StorySegment[];
  isLoading: boolean;
  onRefresh: () => Promise<void>;
  onLoadMore: () => Promise<void>;
  onBranchSelect: (segmentId: string) => Promise<void>;
  hasMoreSegments: boolean;
  showCreationIcons?: boolean;
  onCreateSameLevelSegment?: (parentSegmentId: string) => void;
  onCreateChildSegment?: (parentSegmentId: string) => void;
}

export default function StoryFeed({
  segments,
  isLoading,
  onRefresh,
  onLoadMore,
  onBranchSelect,
  hasMoreSegments,
  showCreationIcons = false,
  onCreateSameLevelSegment,
  onCreateChildSegment,
}: StoryFeedProps) {
  const { t } = useTranslation();

  // Ref for the FlatList
  const flatListRef = useRef<FlatList>(null);

  // State for the active branch carousel
  const [activeBranchSegmentId, setActiveBranchSegmentId] = useState<
    string | null
  >(null);

  // Get branch data for the active segment
  const {
    branches,
    totalBranches,
    isLoading: branchesLoading,
    currentPage,
    hasMorePages,
    loadMore: loadMoreBranches,
    changeFilter,
  } = useBranchCarousel({
    segmentId: activeBranchSegmentId || '',
    pageSize: 5,
  });

  // Handle opening the branch carousel
  const handleOpenBranchCarousel = (segmentId: string) => {
    setActiveBranchSegmentId(segmentId);
  };

  // Handle closing the branch carousel
  const handleCloseBranchCarousel = () => {
    setActiveBranchSegmentId(null);
  };

  // Handle selecting a branch
  const handleBranchSelect = async (branchId: string) => {
    handleCloseBranchCarousel();
    await onBranchSelect(branchId);
  };

  // Render a story node card
  const renderStoryNode = ({
    item,
    index,
  }: {
    item: StorySegment;
    index: number;
  }) => {
    const isFirstNode = index === 0;
    const isLastNode = index === segments.length - 1;
    const showBranchCarousel = activeBranchSegmentId === item.id;

    // 使用工具函数获取子分支数量
    const childrenCount = getChildrenCount(item.children_count);
    const hasBranches = childrenCount > 0;

    // 调试日志
    console.log(
      'StoryFeed - segment:',
      item.id,
      'children_count raw:',
      JSON.stringify(item.children_count),
      'processed childrenCount:',
      childrenCount,
      'hasBranches:',
      hasBranches
    );

    return (
      <View>
        <View className="relative">
          <View className="relative">
            <StoryNodeCard
              segment={item}
              isOriginStory={isFirstNode}
              onBranchPress={() => {
                console.log(
                  'Branch button pressed for segment:',
                  item.id,
                  'hasBranches:',
                  hasBranches
                );
                // Always open the carousel when the button is clicked, even if there are no branches yet
                handleOpenBranchCarousel(item.id);
              }}
              hasBranches={hasBranches}
            />

            {/* 下级创作按钮 - 显示在段落底部边框中间 */}
            {showCreationIcons && isLastNode && (
              <View className="absolute bottom-[-1px] left-0 right-0 h-[2px] items-center justify-center z-10">
                <M3EButtonFilled
                 
                  icon="add"
                  onPress={() => onCreateChildSegment?.(item.id)}
                  style={{
                    width: 32,
                    height: 32,
                    borderRadius: 16,
                  }}
                />
              </View>
            )}
          </View>

          {/* 同级创作按钮 - 显示在段落右侧中间 */}
          {showCreationIcons && !isFirstNode && (
            <M3EButtonFilled
             
              icon="add"
              onPress={() =>
                onCreateSameLevelSegment?.(item.parent_segment_id || '')
              }
              style={{
                position: 'absolute',
                right: -16,
                top: '50%',
                marginTop: -16,
                width: 32,
                height: 32,
                borderRadius: 16,
              }}
            />
          )}
        </View>

        {showBranchCarousel && (
          <BranchCarousel
            segmentId={item.id}
            branches={branches}
            totalBranches={totalBranches}
            isLoading={branchesLoading}
            currentPage={currentPage}
            hasMorePages={hasMorePages}
            onBranchSelect={handleBranchSelect}
            onClose={handleCloseBranchCarousel}
            onLoadMore={loadMoreBranches}
            onFilterChange={changeFilter}
          />
        )}
      </View>
    );
  };

  // Render the loading footer
  const renderFooter = () => {
    if (!isLoading) return null;

    return (
      <View className="p-4 flex-row items-center justify-center">
        <M3EProgressIndicator size="small" color="$primary500" />
        <Text className="ml-2 text-sm text-secondary-600 dark:text-secondary-400">
          {t('storyDetail.loadingMore', 'Loading more...')}
        </Text>
      </View>
    );
  };

  // Render the empty state
  const renderEmpty = () => {
    if (isLoading) return null;

    return (
      <View className="py-8 px-6 items-center justify-center">
        <Text className="text-base font-medium text-secondary-600 dark:text-secondary-400 text-center">
          {t('storyDetail.noSegments', 'No story segments found')}
        </Text>
      </View>
    );
  };

  return (
    <FlatList
      ref={flatListRef}
      data={segments}
      renderItem={renderStoryNode}
      keyExtractor={(item) => item.id}
      contentContainerStyle={{
        paddingHorizontal: 16,
        paddingVertical: 16,
        flexGrow: 1,
      }}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={isLoading && segments.length === 0}
          onRefresh={onRefresh}
          colors={['#6366f1']} // primary color
          tintColor="#6366f1" // primary color
        />
      }
      onEndReached={() => {
        if (!isLoading && hasMoreSegments) {
          onLoadMore();
        }
      }}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      initialNumToRender={10}
      maxToRenderPerBatch={5}
      windowSize={10}
      removeClippedSubviews={true}
    />
  );
}
