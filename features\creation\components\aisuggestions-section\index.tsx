import React from 'react';
import { View } from 'react-native';

import { Text  } from '@/components/base';
import { M3EButtonFilled, M3EButtonText } from '@/components/ui/m3e-button/m3e-buttons';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { useTranslation } from 'react-i18next';

import AiSuggestionCard from '@/components/creation/ai-suggestion-card';

interface AISuggestionsSectionProps {
  onFetchSuggestions: () => void;
  loadingSuggestions: boolean;
  showSuggestions: boolean;
  suggestions: string[];
  onSelectSuggestion: (suggestion: string) => void;
  disabled?: boolean;
}

export default function AISuggestionsSection({
  onFetchSuggestions,
  loadingSuggestions,
  showSuggestions,
  suggestions,
  onSelectSuggestion,
  disabled = false,
}: AISuggestionsSectionProps) {
  const { t } = useTranslation();

  return (
    <View className="mb-6">
      <M3EButtonFilled
        action="secondary"
        variant="solid"
       
        onPress={onFetchSuggestions}
        isDisabled={loadingSuggestions || disabled}
        className="bg-accent-500 dark:bg-accent-600 py-4 rounded-md items-center justify-center mb-6 w-full"
      >
        {loadingSuggestions ? (
          <M3EProgressIndicator color="white" size="small" />
        ) : (
          <M3EButtonText className="text-base font-medium text-white">
            {t('storyForm.getAISuggestions', '获取 AI 建议')}
          </M3EButtonText>
        )}
      </M3EButtonFilled>

      {showSuggestions && loadingSuggestions && (
        <M3EProgressIndicator size="large" color="primary" className="mt-4 self-center" />
      )}

      {showSuggestions && !loadingSuggestions && suggestions.length > 0 && (
        <View className="mt-4">
          <Text
            size="md"
            className="font-medium text-typography-900 dark:text-typography-50 mb-2"
          >
            {t('aiSuggestions.title', 'AI 建议:')}
          </Text>
          <View className="flex flex-col" space="sm">
            {suggestions.map((suggestion, index) => (
              <AiSuggestionCard
                key={index}
                suggestion={suggestion}
                onSelect={onSelectSuggestion}
              />
            ))}
          </View>
        </View>
      )}

      {showSuggestions && !loadingSuggestions && suggestions.length === 0 && (
        <Text size="sm" className="text-typography-500 mt-4 text-center">
          {t(
            'aiSuggestions.noSuggestions',
            '暂时没有合适的建议，尝试修改你的输入或稍后再试。'
          )}
        </Text>
      )}
    </View>
  );
}
