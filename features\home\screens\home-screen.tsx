import React from 'react';
import { ScrollView, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { mockThemes } from '@/utils/mock-data';
import { M3ESearch } from '@/components/ui/m3e-search';
import HeaderBar from '@/components/ui/header-bar';
import { BarChart2 } from 'lucide-react-native';
import { useRouter } from 'expo-router';

import { Text, Pressable } from '@/components/base';
import { SafeAreaView } from '@/components/ui/safe-area-view';

// Import feature components
import { ThemeCarousel } from '@/features/home/<USER>/theme-carousel';
import { HomeScreenLoading } from '@/features/home/<USER>/home-screen-loading';
import { HomeScreenError } from '@/features/home/<USER>/home-screen-error';
import { HomeScreenContent } from '@/features/home/<USER>/home-screen-content';
import { useHomeScreenData } from '@/features/home/<USER>/use-home-screen-data';

export default function HomeScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = React.useState('');

  const {
    storyListTabs,
    activeStoryListTab,
    setActiveStoryListTab,
    stories,
    featuredStory,
    isLoading,
    error,
    fetchStories,
    mapTabToFilter,
  } = useHomeScreenData();

  const themes = mockThemes;

  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    router.push(`/stories/${storyId}`);
  };

  const handleThemePress = (themeId: string) => {
    console.log('Theme pressed:', themeId);
    // 导航到主题搜索结果
    router.push(`/search?topic=${encodeURIComponent(themeId)}`);
  };

  const handleSearch = (text: string) => {
    if (text.trim()) {
      // 导航到搜索结果页面
      router.push(`/search?q=${encodeURIComponent(text)}`);
    }
  };

  const renderContent = () => {
    if (isLoading && stories.length === 0) {
      return <HomeScreenLoading />;
    }

    if (error) {
      return (
        <HomeScreenError
          error={error}
          onRetry={() => fetchStories(mapTabToFilter(activeStoryListTab))}
        />
      );
    }

    return (
      <HomeScreenContent
        featuredStory={featuredStory}
        stories={stories}
        storyListTabs={storyListTabs}
        activeStoryListTab={activeStoryListTab}
        onTabPress={setActiveStoryListTab}
        onStoryPress={handleStoryPress}
      />
    );
  };

  return (
    <SafeAreaView
      className="flex-1 bg-background-50 dark:bg-background-900"
      edges={['top', 'left', 'right']}
    >
      <HeaderBar
        title={t('appName')}
        subtitle={t('homeScreen.subtitle')}
        rightElement={
          <Pressable className="p-1" onPress={() => router.push('/rankings')}>
            <BarChart2
              size={24}
              className="text-typography-900 dark:text-typography-100"
            />
          </Pressable>
        }
      />

      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingBottom: 32,
        }}
      >
        <View className="my-4">
          <M3ESearch
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSearch={handleSearch}
            placeholder={t(
              'homeScreen.searchPlaceholder',
              '搜索故事、作者、标签...'
            )}
          />
        </View>

        <Text className="text-xl font-bold text-typography-900 dark:text-typography-100 mt-6 mb-4">
          {t('homeScreen.sectionTitle.themes')}
        </Text>
        <ThemeCarousel themes={themes} onThemePress={handleThemePress} />

        {renderContent()}
      </ScrollView>
    </SafeAreaView>
  );
}
