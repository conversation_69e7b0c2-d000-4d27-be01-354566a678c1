import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';
import { M3EBodyMedium } from '@/components/ui/m3e-typography';

export function StoryDetailEmpty() {
  const { t } = useTranslation();
  const { colors } = useUnifiedTheme();

  return (
    <View className="flex-1 justify-center items-center p-6">
      <M3EBodyMedium color={colors.onSurfaceVariant}>
        {t('storyDetail.errors.notFound', 'Story not found.')}
      </M3EBodyMedium>
    </View>
  );
}
