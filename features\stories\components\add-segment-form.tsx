import React from 'react';
import { Alert , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { GitBranch } from 'lucide-react-native';

import { Text, TextArea } from '@/components/base';
import { M3EButtonFilled, M3EButtonText } from '@/components/ui/m3e-button/m3e-buttons';
import { M3EIconButton } from '@/components/ui/m3e-button/m3e-buttons';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

interface AddSegmentFormProps {
  segmentContent: string;
  onContentChange: (text: string) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  onCreateBranchClick?: () => void;
}

export default function AddSegmentForm({
  segmentContent,
  onContentChange,
  onSubmit,
  isSubmitting,
  onCreateBranchClick,
}: AddSegmentFormProps) {
  const { t } = useTranslation();

  const isDisabled = isSubmitting || !segmentContent.trim();

  // 处理创建分支按钮点击
  const handleCreateBranchClick = () => {
    if (!segmentContent.trim()) {
      Alert.alert(
        t('storyDetail.errors.title', 'Error'),
        t('storyDetail.errors.emptyBranch', 'Cannot create an empty branch.')
      );
      return;
    }

    if (onCreateBranchClick) {
      onCreateBranchClick();
    }
  };

  return (
    <View className="pt-4 border-t border-outline-300 mt-6">
      <Text className="text-lg font-bold text-typography-900 mb-4">
        {t('storyDetail.continueStory', 'Continue the Story')}
      </Text>
      <TextArea
        className="bg-background-100 text-typography-900 p-4 rounded-md border border-outline-300 mb-4 text-base min-h-[120px]"
        placeholder={t('storyDetail.addYourPart', 'Add your part...')}
        value={segmentContent}
        onChangeText={onContentChange}
        isDisabled={isSubmitting}
      />
      <View className="flex-row justify-between items-center">
        {onCreateBranchClick && (
          <M3EButtonFilled
            className={`flex-1 flex-row items-center justify-center min-h-[48px] ${
              isDisabled ? 'bg-background-400 opacity-70' : 'bg-tertiary-500'
            }`}
            onPress={handleCreateBranchClick}
            isDisabled={isDisabled}
          >
            <M3EIconButton icon={<GitBranch size="sm" color="white"  />} />
            <M3EButtonText className="text-base font-bold text-white ml-1">
              {t('storyDetail.createBranch', 'Create Branch')}
            </M3EButtonText>
          </M3EButtonFilled>
        )}

        <M3EButtonFilled
          className={`flex-1 min-h-[48px] ${
            isDisabled ? 'bg-background-400 opacity-70' : 'bg-primary-500'
          } ${onCreateBranchClick ? 'ml-4' : ''}`}
          onPress={onSubmit}
          isDisabled={isDisabled}
        >
          {isSubmitting ? (
            <M3EProgressIndicator size="small" color="white" />
          ) : (
            <M3EButtonText className="text-base font-bold text-white">
              {t('storyDetail.addSegment', 'Add Your Part')}
            </M3EButtonText>
          )}
        </M3EButtonFilled>
      </View>

      {/* AI Suggestions Section has been moved to AISuggestionBlock component */}
    </View>
  );
}
