import React, { useEffect } from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useSettingsStore, ThemeMode } from '@/lib/store/settings-store';
import { useTheme } from '@/lib/theme/nativewind-theme-provider';

import { Pressable } from '@/components/base';
import { M3EText } from '@/components/ui/m3e-typography';

export function ThemeOptionsGroup() {
  const { t } = useTranslation('settings');
  const themeMode = useSettingsStore((state) => state.themeMode);
  const setThemeMode = useSettingsStore((state) => state.setThemeMode);
  const { colors, mode, setMode } = useUnifiedTheme();

  // 同步设置存储和新主题系统
  useEffect(() => {
    console.log('Theme sync check:', {
      settingsThemeMode: themeMode,
      currentMode: mode,
    });

    // 如果设置存储和主题系统不同步，则同步
    if (themeMode !== mode) {
      setMode(themeMode);
    }
  }, [themeMode, mode, setMode]);

  // Define theme options
  const themeOptions: { value: ThemeMode; label: string }[] = [
    { value: 'light', label: t('lightMode') },
    { value: 'dark', label: t('darkMode') },
    { value: 'system', label: t('systemMode') },
  ];

  const handleThemeChange = (newTheme: ThemeMode) => {
    console.log('Theme change requested:', newTheme, 'current mode:', mode);

    // 立即更新设置存储
    setThemeMode(newTheme);

    // 立即同步更新新主题系统
    setMode(newTheme);

    console.log('Theme change applied:', newTheme);
  };

  return (
    <View style={{ width: '100%', flexDirection: 'row' }}>
      {themeOptions.map((option) => (
        <Pressable
          key={option.value}
          onPress={() => handleThemeChange(option.value)}
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 16,
            backgroundColor:
              themeMode === option.value ? colors.primary : colors.surface,
          }}
        >
          <M3EText
            variant="labelLarge"
            color={
              themeMode === option.value ? colors.onPrimary : colors.onSurface
            }
          >
            {option.label}
          </M3EText>
        </Pressable>
      ))}
    </View>
  );
}
