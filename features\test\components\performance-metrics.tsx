import React from 'react';
import { View } from 'react-native';
import { useRouter } from 'expo-router';

import { Text  } from '@/components/base';
import { M3EButtonFilled } from '@/components/ui/m3e-button/m3e-buttons';

interface PerformanceMetricsProps {
  dataSetSize: number;
  renderTime: number;
  memoryUsage: string;
  dataSizes: number[];
  onDataSizeChange: (size: number) => void;
}

export default function PerformanceMetrics({
  dataSetSize,
  renderTime,
  memoryUsage,
  dataSizes,
  onDataSizeChange,
}: PerformanceMetricsProps) {
  const router = useRouter();

  return (
    <View className="p-4 bg-background-100 dark:bg-background-800 border-b border-outline-200 dark:border-outline-700">
      <Text className="text-lg font-bold text-typography-900 dark:text-typography-100 mb-2">
        Performance Test
      </Text>

      <View className="flex-row justify-between mb-4">
        <View className="flex-1 bg-background-50 dark:bg-background-700 rounded-lg p-4 mr-2 border border-outline-200 dark:border-outline-600">
          <Text className="text-xs text-typography-500 dark:text-typography-400 mb-1">
            Data Set Size
          </Text>
          <Text className="text-base font-bold text-typography-900 dark:text-typography-100">
            {dataSetSize} items
          </Text>
        </View>

        <View className="flex-1 bg-background-50 dark:bg-background-700 rounded-lg p-4 mr-2 border border-outline-200 dark:border-outline-600">
          <Text className="text-xs text-typography-500 dark:text-typography-400 mb-1">
            Render Time
          </Text>
          <Text className="text-base font-bold text-typography-900 dark:text-typography-100">
            {renderTime.toFixed(2)} ms
          </Text>
        </View>

        <View className="flex-1 bg-background-50 dark:bg-background-700 rounded-lg p-4 border border-outline-200 dark:border-outline-600">
          <Text className="text-xs text-typography-500 dark:text-typography-400 mb-1">
            Memory Usage
          </Text>
          <Text className="text-base font-bold text-typography-900 dark:text-typography-100">
            {memoryUsage}
          </Text>
        </View>
      </View>

      <View className="mb-4">
        <Text className="text-sm font-bold text-typography-900 dark:text-typography-100 mb-2">
          Data Set Size
        </Text>

        <View className="flex-row flex-wrap gap-2">
          {dataSizes.map((size) => (
            <M3EButtonFilled
              key={size}
              className={`px-4 py-2 rounded-lg border ${
                dataSetSize === size
                  ? 'bg-primary-100 dark:bg-primary-800 border-primary-500 dark:border-primary-600'
                  : 'bg-transparent border-outline-300 dark:border-outline-600'
              }`}
              onPress={() => onDataSizeChange(size)}
            >
              <Text
                className={`text-sm ${
                  dataSetSize === size
                    ? 'text-primary-700 dark:text-primary-300'
                    : 'text-typography-700 dark:text-typography-300'
                }`}
              >
                {size} items
              </Text>
            </M3EButtonFilled>
          ))}
        </View>
      </View>

      <M3EButtonFilled
        className="mt-4 p-4 bg-primary-500 dark:bg-primary-600 rounded-lg items-center"
        onPress={() => router.back()}
      >
        <Text className="text-typography-950 dark:text-typography-50 font-bold">
          Back to Tests
        </Text>
      </M3EButtonFilled>
    </View>
  );
}
