import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Animated, PanResponder, FlatList , View } from 'react-native';
import { StorySegment } from '@/api/stories';
import { BranchNode } from '@/api/stories/branches/types';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/lib/store/auth-store';
import { Minus, Plus, RotateCcw } from 'lucide-react-native';

// Import gluestack-ui components

import { Text  } from '@/components/base';

import { M3EButtonFilled, M3EIconButton } from '@/components/ui/m3e-button/m3e-buttons';

// Import custom components
import { BranchNode as BranchNodeComponent } from './branch-node';

interface BranchVisualizerProps {
  branchTree: BranchNode | null;
  currentSegmentId?: string;
  onBranchSelect: (segmentId: string) => void;
  onRenameBranch?: (
    segmentId: string,
    branchTitle: string
  ) => Promise<StorySegment | null>;
  onDeleteBranch?: (segmentId: string) => Promise<boolean>;
  collapsible?: boolean;
  initialScale?: number;
  visualizationStyle?: 'tree' | 'flow' | 'network' | 'timeline';
}

export default function BranchVisualizer({
  branchTree,
  currentSegmentId,
  onBranchSelect,
  onRenameBranch,
  onDeleteBranch,
  collapsible = true,
  initialScale = 1,
  // visualizationStyle is used in the interface but not in this implementation yet
  visualizationStyle = 'tree',
}: BranchVisualizerProps) {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [scale, setScale] = useState(initialScale);
  const pan = useRef(new Animated.ValueXY()).current;

  // 状态：扁平化的分支列表（用于优化大型分支结构的性能）
  const [flattenedBranches, setFlattenedBranches] = useState<
    { node: BranchNode; depth: number; isVisible: boolean }[]
  >([]);

  // 将树状结构转换为扁平列表
  const flattenBranchTree = useCallback(
    (
      node: BranchNode | null,
      depth = 0,
      isVisible = true,
      parentExpanded = true
    ) => {
      if (!node) return [];

      const result: {
        node: BranchNode;
        depth: number;
        isVisible: boolean;
      }[] = [];

      // 当前节点是否可见（父节点展开且当前节点可见）
      const nodeIsVisible = isVisible && parentExpanded;

      // 添加当前节点
      result.push({
        node,
        depth,
        isVisible: nodeIsVisible,
      });

      // 当前节点是否展开
      const isExpanded = expandedNodes.has(node.id);

      // 递归添加子节点
      if (node.children && node.children.length > 0) {
        node.children.forEach((childNode) => {
          result.push(
            ...flattenBranchTree(
              childNode,
              depth + 1,
              nodeIsVisible,
              isExpanded && nodeIsVisible
            )
          );
        });
      }

      return result;
    },
    [expandedNodes]
  );

  // 处理节点展开/折叠
  const toggleNode = useCallback((nodeId: string) => {
    setExpandedNodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 更新扁平化的分支列表
  useEffect(() => {
    if (branchTree) {
      const flattened = flattenBranchTree(branchTree);
      setFlattenedBranches(flattened);
    }
  }, [branchTree, expandedNodes, flattenBranchTree]);

  // 平移和缩放手势
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        pan.setOffset({
          x: (pan.x as any)._value,
          y: (pan.y as any)._value,
        });
        pan.setValue({ x: 0, y: 0 });
      },
      onPanResponderMove: Animated.event([null, { dx: pan.x, dy: pan.y }], {
        useNativeDriver: false,
      }),
      onPanResponderRelease: () => {
        pan.flattenOffset();
      },
    })
  ).current;

  // 处理缩放
  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.1, 0.5));
  };

  // 重置视图
  const resetView = () => {
    pan.setValue({ x: 0, y: 0 });
    setScale(initialScale);
  };

  // 渲染单个分支节点项
  const renderBranchNodeItem = ({
    item,
  }: {
    item: { node: BranchNode; depth: number; isVisible: boolean };
  }) => {
    if (!item.isVisible) return null;

    const isSelected = item.node.id === currentSegmentId;
    const hasChildren = item.node.children && item.node.children.length > 0;
    const isExpanded = expandedNodes.has(item.node.id);
    const isAuthor = user?.id === item.node.segment.author_id;

    return (
      <BranchNodeComponent
        node={item.node}
        depth={item.depth}
        isExpanded={isExpanded}
        isSelected={isSelected}
        hasChildren={hasChildren}
        collapsible={collapsible}
        isAuthor={isAuthor}
        onToggle={toggleNode}
        onSelect={onBranchSelect}
        onRenameBranch={onRenameBranch}
        onDeleteBranch={onDeleteBranch}
      />
    );
  };

  // 如果没有分支，显示空态
  if (!branchTree) {
    return (
      <View className="p-6 bg-surface-50 dark:bg-surface-900 rounded-md border border-outline-200 dark:border-outline-700 my-4">
        <Text className="text-base text-secondary-500 dark:text-secondary-400 text-center">
          {t('storyDetail.noBranches', 'No branches available')}
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
      <View className="flex flex-row justify-end p-2 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
        <M3EButtonFilled
          size="sm"
          variant="outline"
         
          className="w-8 h-8 rounded-full justify-center items-center mx-0.5"
          onPress={handleZoomOut}
        >
          <M3EIconButton icon={<Minus size="sm"  />} />
        </M3EButtonFilled>
        <M3EButtonFilled
          size="sm"
          variant="outline"
         
          className="w-8 h-8 rounded-full justify-center items-center mx-0.5"
          onPress={handleZoomIn}
        >
          <M3EIconButton icon={<Plus size="sm"  />} />
        </M3EButtonFilled>
        <M3EButtonFilled
          size="sm"
          variant="outline"
         
          className="w-8 h-8 rounded-full justify-center items-center mx-0.5 ml-2"
          onPress={resetView}
        >
          <M3EIconButton icon={<RotateCcw size="sm"  />} />
        </M3EButtonFilled>
      </View>

      <Animated.View
        {...panResponder.panHandlers}
        style={{
          transform: [
            { translateX: pan.x },
            { translateY: pan.y },
            { scale: scale },
          ],
        }}
      >
        <View className="p-4 min-w-full">
          <FlatList
            data={flattenedBranches.filter(
              (item: { node: BranchNode; depth: number; isVisible: boolean }) =>
                item.isVisible
            )}
            renderItem={renderBranchNodeItem}
            keyExtractor={(item: {
              node: BranchNode;
              depth: number;
              isVisible: boolean;
            }) => item.node.id}
            initialNumToRender={20}
            maxToRenderPerBatch={10}
            windowSize={5}
            removeClippedSubviews={true}
            getItemLayout={(_data: any, index: number) => ({
              length: 60,
              offset: 60 * index,
              index,
            })}
          />
        </View>
      </Animated.View>
    </View>
  );
}
